/**
 * @Author:          Zack
 * @Date:            2018-02-23
 * @Description:
 * @Test_Class:      WarrantyService
 * @Related_Class:
 * @Last_Modified_by: <PERSON>
 * @Last_Modified_time: 2018-08-13 14:34:54
 * @Modifiy_Purpose:    WarrantyService发生更改，相应更改测试类
 */
@isTest
private class WarrantyServiceTest {
	/**
	 * @Author:     Zack
	 * @Date:       2018-02-23
	 * @Return:     
	 * @Function:   测试WarrantyService的构造方法、setStoreReturnExchangePolicy、setWarrantyItemExpirationDate等方法
	 * @Last_Modified_by:  Zack
 	 * @Last_Modified_time:2018-07-12
 	 * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
	 */
    static testMethod void testMethodsOne() {
    	//========初始化数据========
    	//1、创建Account数据
    	Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        Account_SyncToShopify obj = new Account_SyncToShopify();
        Account_SyncToShopify.functionName.add('Account');
        CCM_SharingUtil.isSharingOnly = true;
    	insert acc;

    	//2、创建Product数据
    	Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;
        
        System_Configuration__c sc = new System_Configuration__c();
        sc.Name = 'Claim Auto Approval';
        sc.Is_Active__c = true;
        insert sc;


    	//4、创建Warranty数据
    	Warranty__c war = TestDataFactory.createWarranty();
    	war.AccountCustomer__c = acc.Id;
    	war.Store_return_exchange_policy__c = Date.today().addDays(60);
    	war.Purchase_Date__c = Date.today();
    	war.Master_Product__c = pro.Id;
    	insert war;

		Test.startTest();
    	//5、创建warranty item数据
    	Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    	wi.Product_Model__c = '3';
    	wi.Warranty__c = war.Id;
    	// insert wi;
		Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Product_Type__c = 'Product';
        // insert wi2;
        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        // insert wi3;
        Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
        wi4.Product_Model__c = '3';
        wi4.Warranty__c = war.Id;
        // insert wi4;
        Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
        wi5.Warranty__c = war.Id;
        wi5.Product_Type__c = 'Product';
        // insert wi5;
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

    	List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    	wiList.add(wi);
        wiList.add(wi2);
        wiList.add(wi3);
        wiList.add(wi4);
    	wiList.add(wi5);
		insert wiList;
    	//6、创建SystemConfiguration
    	TestDataFactory.createSystemConfig('v3');
			
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //2、测试setStoreReturnExchangePolicy方法
        //  测试BrandName为skil的情况
        war.Brand_Name__c = 'Skil';
        /* war.Place_of_Purchase_picklist__c = 'Wal-mart';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        war.Place_of_Purchase_picklist__c = 'Menards';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        /*war.Place_of_Purchase_picklist__c = 'Ferguson';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为未知的情况
        war.Brand_Name__c = 'other';
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //3、测试setWarrantyItemExpirationDate方法
        ws.warrantyItemList = wiList;
        //  测试Brand Name为Skil的情况
        war.Brand_Name__c = 'Skil';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        //  测试Brand Name为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        //  测试Brand Name为未知的情况
        war.Brand_Name__c = 'Unknown';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        Test.stopTest();
        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        
        ws.setStoreReturnExchangePolicy();
        Attachment att = new Attachment();
        att.Body = Blob.valueOf( 'Testing' );
        att.Name = 'test att';
        att.ParentId = war.Id;
        insert att;
        ws.receiptAttachmentID = att.Id;
        ws.relateWarrantyAndAttachment();
    
        Set<String> warrantyIdSet = new Set<String>();
        warrantyIdSet.add(war.Id);
        WarrantyService.getWarrantyNoItemsMapByIdSet(warrantyIdSet);
        
    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    static testMethod void testMethodsOne2() {
        //========初始化数据========
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;

        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 66;
        insert pbe;


        //4、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //5、创建warranty item数据
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

        //6、创建SystemConfiguration
        TestDataFactory.createSystemConfig('v3');
		
        Test.startTest();
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //4、测试countCost方法
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.warranty = war;
        ws.countCost();

        //5、测试saveWarrantyItem方法
        List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi6);
        ws.warrantyItemList = wiList2;
        ws.saveWarrantyItem();

        wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi7);
        ws.warrantyItemList = wiList2;
        ws.masterProductOld = war.Master_Product__r.Id;
        ws.saveWarrantyItem();

        //6、测试uploadReceiptAttachment方法
        ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

        //7、测试relateWarrantyAndAttachment方法
        //******
        //ws.relateWarrantyAndAttachment();
        Test.stopTest();

    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    // static testMethod void testMethodsOne3() {
    //     //========初始化数据========
    //     //1、创建Account数据
    //     Account acc = TestDataFactory.createAccount();
    //     insert acc;

    //     //2、创建Product数据
    //     Product2 pro = TestDataFactory.createProduct();
    //     insert pro;

    //     //3、创建Price Book数据
    //     Default_PriceBook__c dp = new Default_PriceBook__c();
    //     dp.Place_of_Purchase__c = 'Home Depot';
    //     dp.Name = 'HomeDepot';
    //     insert dp;

    //     Default_PriceBook__c dp2 = new Default_PriceBook__c();
    //     dp2.Place_of_Purchase__c = 'Amazon LLC';
    //     dp2.Name = 'AmazonLLc';
    //     insert dp2;

    //     Default_PriceBook__c dp3 = new Default_PriceBook__c();
    //     dp3.Place_of_Purchase__c = 'Ferguson';
    //     dp3.Name = 'Ferguson';
    //     insert dp3;

    //     Default_PriceBook__c dp4 = new Default_PriceBook__c();
    //     dp4.Place_of_Purchase__c = 'Other';
    //     dp4.Name = 'Other';
    //     insert dp4;

    //     /*PricebookEntry pbe = new PricebookEntry();
    //     pbe.Pricebook2Id = dp.Id;
    //     pbe.Product2Id = pro.Id;
    //     pbe.UnitPrice = 66;
    //     insert pbe;

    //     PricebookEntry pbe2 = new PricebookEntry();
    //     pbe2.Pricebook2Id = dp2.Id;
    //     pbe2.Product2Id = pro.Id;
    //     pbe2.UnitPrice = 66;
    //     insert pbe2;

    //     PricebookEntry pbe3 = new PricebookEntry();
    //     pbe3.Pricebook2Id = dp3.Id;
    //     pbe3.Product2Id = pro.Id;
    //     pbe3.UnitPrice = 66;
    //     insert pbe3;*/

    //     //4、创建Warranty数据
    //     Warranty__c war = TestDataFactory.createWarranty();
    //     war.AccountCustomer__c = acc.Id;
    //     war.Store_return_exchange_policy__c = Date.today().addDays(60);
    //     war.Purchase_Date__c = Date.today();
    //     war.Master_Product__c = pro.Id;
    //     insert war;

    //     //5、创建warranty item数据
    //     Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    //     wi.Product_Model__c = '3';
    //     wi.Warranty__c = war.Id;
    //     insert wi;
    //     Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi2.Product_Type__c = 'Product';
    //     insert wi2;
    //     Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
    //     insert wi3;
    //     Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi4.Product_Model__c = '3';
    //     wi4.Warranty__c = war.Id;
    //     insert wi4;
    //     Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi5.Warranty__c = war.Id;
    //     wi5.Product_Type__c = 'Product';
    //     insert wi5;
    //     Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi6.Warranty__c = war.Id;
    //     Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi7.Warranty__c = war.Id;

    //     List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    //     wiList.add(wi);
    //     wiList.add(wi2);
    //     wiList.add(wi3);
    //     wiList.add(wi4);
    //     wiList.add(wi5);

    //     //6、创建SystemConfiguration
    //     TestDataFactory.createSystemConfig('v3');

    //     //========开始测试========
    //     //1、测试构造方法
    //     WarrantyService ws = new WarrantyService();

    //     //4、测试countCost方法
    //     war.Place_of_Purchase_picklist__c = 'Unknown';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Home Depot';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Amazon LLC';
    //     ws.warranty = war;
    //     ws.countCost();

    //     //5、测试saveWarrantyItem方法
    //     List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi6);
    //     ws.warrantyItemList = wiList2;
    //     ws.saveWarrantyItem();

    //     wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi7);
    //     ws.warrantyItemList = wiList2;
    //     ws.masterProductOld = war.Master_Product__r.Id;
    //     ws.saveWarrantyItem();

    //     //6、测试uploadReceiptAttachment方法
    //     ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

    //     //7、测试relateWarrantyAndAttachment方法
    //     //******
    //     //ws.relateWarrantyAndAttachment();
    // }

    /**
     * @Author:     Zack
     * @Date:       2018-07-12
     * @Return:
     * @Function:   测试WarrantyService的verifySerialNumber、getWarrantyByID、getWarrantySimpleByID、
     *              getWarrantyMapByIdSet、getWarrantyAndOrderById、getWarrantyAndOrderByIdSet、
     *              getWarrantyItemByWarrantyId、getWarrantyByWarrantyIds、getWarrantyByWarrantyName等方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:
     */
    static testMethod void testMethodsTwo(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //4、创建System Configuration数据
        System_Configuration__c sc = TestDataFactory.createSystemConfig('SN_Format');
        sc.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        sc.RegExp__c = '^[ACU]([0-9]{2})((0[1-9])|(1[0-2]))[0-9]{5}$';
        sc.Name = 'EGO Skil SkilSaw';
        insert sc;
			
        Test.startTest();
        //================开始测试================
        //1、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('');
        WarrantyService.verifySerialNumber('NBM011506000001X');

        //2、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('EGO', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('SkilSaw', 'NBM011506000001X', null);

        //3、测试getWarrantyByID方法
        WarrantyService.getWarrantyByID(war.Id);
        WarrantyService.getWarrantyByID('');

        //4、测试getWarrantySimpleByID方法
        WarrantyService.getWarrantySimpleByID(war.Id);
        WarrantyService.getWarrantySimpleByIDforCase(war.Id);
        WarrantyService.getWarrantySimpleByID('');

        //5、测试getWarrantyMapByIdSet方法
        Set<String> warIdSet = new Set<String>();
        warIdSet.add(war.Id);
        WarrantyService.getWarrantyMapByIdSet(warIdSet);

        //6、测试getWarrantyAndOrderById方法
        WarrantyService.getWarrantyAndOrderById(war.Id);

        //7、测试getWarrantyAndOrderByIdSet方法
        WarrantyService.getWarrantyAndOrderByIdSet(warIdSet);

        //8、测试getWarrantyItemByWarrantyId方法
        WarrantyService.getWarrantyItemByWarrantyId(war.Id);

        //9、测试getWarrantyByWarrantyIds方法
        WarrantyService.getWarrantyByWarrantyIds(war.Id);

        //10、测试getWarrantyByWarrantyName方法
        WarrantyService.getWarrantyByWarrantyName(war.Name);
        
        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setStoreReturnExchangePolicy方法 - 覆盖不同品牌和购买地的场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetStoreReturnExchangePolicy(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试Skil品牌的不同购买地
        ws.warranty.Brand_Name__c = 'Skil';
        ws.warranty.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Home Depot购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Lowes';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Lowes购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Menards';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Menards购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Meijer';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Meijer购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Wal-mart';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Wal-mart购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Amazon LLC购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'White Cap';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌White Cap购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Tool Barn';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Tool Barn购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Acme Tools';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Acme Tools购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Authorized Dealer购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Other';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(-1), ws.warranty.Store_return_exchange_policy__c, '测试Skil品牌Other购买地失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setStoreReturnExchangePolicy方法 - SkilSaw品牌场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetStoreReturnExchangePolicySkilSaw(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试SkilSaw品牌的不同购买地
        ws.warranty.Brand_Name__c = 'SkilSaw';
        ws.warranty.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Home Depot购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Lowes';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Lowes购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Menards';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Menards购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Ferguson';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(61), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Ferguson购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Amazon LLC购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Fastenal';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Fastenal购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Grainger';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Grainger购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'White Cap';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌White Cap购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Authorized Dealer购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Other';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(-1), ws.warranty.Store_return_exchange_policy__c, '测试SkilSaw品牌Other购买地失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setStoreReturnExchangePolicy方法 - EGO品牌场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetStoreReturnExchangePolicyEGO(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试EGO品牌的不同购买地
        ws.warranty.Brand_Name__c = 'EGO';
        ws.warranty.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(91), ws.warranty.Store_return_exchange_policy__c, '测试EGO品牌Home Depot购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试EGO品牌Amazon LLC购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试EGO品牌Authorized Dealer购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Ace Hardware';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(31), ws.warranty.Store_return_exchange_policy__c, '测试EGO品牌Ace Hardware购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = 'Other';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(ws.warranty.Purchase_Date__c.addDays(-1), ws.warranty.Store_return_exchange_policy__c, '测试EGO品牌Other购买地失败');

        // 测试空值情况
        ws.warranty.Purchase_Date__c = null;
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(null, ws.warranty.Store_return_exchange_policy__c, '测试空购买日期失败');

        ws.warranty.Purchase_Date__c = Date.today();
        ws.warranty.Place_of_Purchase_picklist__c = null;
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(null, ws.warranty.Store_return_exchange_policy__c, '测试空购买地失败');

        ws.warranty.Place_of_Purchase_picklist__c = '';
        ws.setStoreReturnExchangePolicy();
        System.assertEquals(null, ws.warranty.Store_return_exchange_policy__c, '测试空字符串购买地失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemExpirationDate方法 - EGO品牌场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetWarrantyItemExpirationDateEGO(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();
        pro.Warranty_Year__c = 5;
        update pro;

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'EGO';
        war.Product_Use_Type2__c = 'Residential';
        insert war;

        //4、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;
        wi1.Product_Type__c = 'Product';
        wi1.Product_Model__c = '1';

        Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Warranty__c = war.Id;
        wi2.Product__c = pro.Id;
        wi2.Product_Type__c = 'Battery';
        wi2.Product_Model__c = '1';

        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        wi3.Warranty__c = war.Id;
        wi3.Product__c = pro.Id;
        wi3.Product_Type__c = 'Product';
        wi3.Product_Model__c = '3';

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1, wi2, wi3};

        // 测试EGO品牌民用产品
        ws.setWarrantyItemExpirationDate(true);

        // 验证结果
        System.assertEquals(war.Purchase_Date__c.addYears(5), wi1.Expiration_Date__c, '测试EGO民用Product类型失败');
        System.assertEquals(war.Purchase_Date__c.addYears(3), wi2.Expiration_Date__c, '测试EGO民用Battery类型失败');
        System.assertEquals(war.Purchase_Date__c.addYears(1), wi3.Expiration_Date__c, '测试EGO民用翻新机失败');

        // 测试EGO品牌商用产品
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        System.assertEquals(war.Purchase_Date__c.addYears(1), wi1.Expiration_Date__c, '测试EGO商用Product类型失败');
        System.assertEquals(war.Purchase_Date__c.addYears(1), wi2.Expiration_Date__c, '测试EGO商用Battery类型失败');
        System.assertEquals(war.Purchase_Date__c.addDays(90), wi3.Expiration_Date__c, '测试EGO商用翻新机失败');

        // 测试空购买日期
        war.Purchase_Date__c = null;
        ws.warranty = war;
        Date originalDate = wi1.Expiration_Date__c;
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(originalDate, wi1.Expiration_Date__c, '测试空购买日期失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemExpirationDate方法 - Skil和SkilSaw品牌场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetWarrantyItemExpirationDateSkil(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();
        pro.Warranty_Year__c = 2;
        update pro;

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'Skil';
        war.Product_Use_Type2__c = 'Residential';
        insert war;

        //4、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1};

        // 测试Skil品牌民用产品
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(war.Purchase_Date__c.addYears(2), wi1.Expiration_Date__c, '测试Skil民用产品失败');

        // 测试Skil品牌商用产品
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(war.Purchase_Date__c.addDays(90), wi1.Expiration_Date__c, '测试Skil商用产品失败');

        // 测试SkilSaw品牌民用产品
        war.Brand_Name__c = 'SkilSaw';
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(war.Purchase_Date__c.addYears(2), wi1.Expiration_Date__c, '测试SkilSaw民用产品失败');

        // 测试SkilSaw品牌商用产品
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(war.Purchase_Date__c.addYears(1), wi1.Expiration_Date__c, '测试SkilSaw商用产品失败');

        // 测试空购买日期
        war.Purchase_Date__c = null;
        ws.warranty = war;
        Date originalDate = wi1.Expiration_Date__c;
        ws.setWarrantyItemExpirationDate(false);
        System.assertEquals(originalDate, wi1.Expiration_Date__c, '测试Skil空购买日期失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的setWarrantyItemIndicator和getPurchasePlaceIsAuthorized方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testSetWarrantyItemIndicator(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'EGO';
        war.Receipt_received_and_verified__c = true;
        war.Lost_Receipt__c = false;
        war.Order_Times__c = 0;
        insert war;

        //4、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;
        wi1.Expiration_Date__c = Date.today().addDays(30);
        wi1.Serial_Number__c = 'TEST123';

        Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Warranty__c = war.Id;
        wi2.Product__c = pro.Id;
        wi2.Expiration_Date__c = Date.today().addDays(-30);
        wi2.Serial_Number__c = 'TEST456';

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1, wi2};

        // 测试Unauthorized购买地
        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi1.Indicator__c, '测试Unauthorized购买地失败');

        // 测试Unknown购买地
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi1.Indicator__c, '测试Unknown购买地未过期失败');

        // 测试Other购买地
        war.Place_of_Purchase_picklist__c = 'Other';
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi1.Indicator__c, '测试Other购买地未过期失败');

        // 测试过期产品
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi2.Indicator__c, '测试过期产品失败');

        // 测试有效保修
        System.assertEquals('Vailid Warranty', wi1.Indicator__c, '测试有效保修失败');

        // 测试无序列号情况
        wi1.Serial_Number__c = null;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi1.Indicator__c, '测试无序列号失败');

        // 测试丢失发票情况
        war.Receipt_received_and_verified__c = false;
        war.Lost_Receipt__c = true;
        war.Order_Times__c = 0;
        ws.warranty = war;
        wi1.Serial_Number__c = 'TEST123';
        wi1.Expiration_Date__c = Date.today().addDays(30);
        ws.setWarrantyItemIndicator();
        System.assertEquals('Lost Receipt', wi1.Indicator__c, '测试丢失发票失败');

        // 测试丢失发票但有订单次数
        war.Order_Times__c = 1;
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Out of Warranty', wi1.Indicator__c, '测试丢失发票有订单次数失败');

        // 测试未上传发票情况
        war.Receipt_received_and_verified__c = false;
        war.Lost_Receipt__c = false;
        ws.warranty = war;
        ws.setWarrantyItemIndicator();
        System.assertEquals('Pending', wi1.Indicator__c, '测试未上传发票失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的getPurchasePlaceIsAuthorized方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testGetPurchasePlaceIsAuthorized(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试Unauthorized情况
        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        String result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unauthorized', result, '测试Unauthorized失败');

        war.Place_of_Purchase_picklist__c = 'unauthorized test';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unauthorized', result, '测试unauthorized小写失败');

        // 测试Unknown情况
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unknown', result, '测试Unknown失败');

        war.Place_of_Purchase_picklist__c = 'unknown test';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unknown', result, '测试unknown小写失败');

        war.Place_of_Purchase_picklist__c = 'Other';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unknown', result, '测试Other失败');

        war.Place_of_Purchase_picklist__c = 'other test';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Unknown', result, '测试other小写失败');

        // 测试Authorized情况
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        result = ws.getPurchasePlaceIsAuthorized();
        System.assertEquals('Authorized', result, '测试Authorized失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的getWarrantyItemProductId方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testGetWarrantyItemProductId(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //4、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1};

        String productId = ws.getWarrantyItemProductId();
        System.assertEquals(pro.Id, productId, '测试获取产品ID失败');

        // 测试空列表
        ws.warrantyItemList = new List<Warranty_Item__c>();
        productId = ws.getWarrantyItemProductId();
        System.assertEquals('', productId, '测试空列表失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的uploadReceiptAttachment和relateWarrantyAndAttachment方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testUploadReceiptAttachment(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;

        // 测试上传附件
        Blob testBlob = Blob.valueOf('测试附件内容');
        Boolean result = ws.uploadReceiptAttachment(testBlob, 'image/jpeg');
        System.assertEquals(true, result, '测试上传附件失败');
        System.assertNotEquals(null, ws.receiptAttachmentID, '测试附件ID失败');
        System.assert(ws.warranty.Image_of_Receipt__c.contains(ws.receiptAttachmentID), '测试附件URL失败');

        // 测试关联附件
        ws.relateWarrantyAndAttachment();

        // 验证附件已关联到warranty
        List<Attachment> attachments = [SELECT Id, ParentId, Name FROM Attachment WHERE ParentId = :war.Id];
        System.assertEquals(1, attachments.size(), '测试关联附件失败');
        System.assertEquals('Recepit Image', attachments[0].Name, '测试附件名称失败');

        // 测试无附件ID的情况
        ws.receiptAttachmentID = null;
        ws.relateWarrantyAndAttachment(); // 应该不会报错

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的verifySerialNumber重载方法
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testVerifySerialNumberAdvanced(){
        //================初始化数据================
        //1、创建System Configuration数据
        System_Configuration__c sc1 = TestDataFactory.createSystemConfig('EGO_v4');
        sc1.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        sc1.RegExp__c = '^[ACU]([0-9]{2})((0[1-9])|(1[0-2]))[0-9]{5}$';
        sc1.Name = 'EGO_v4';
        insert sc1;

        System_Configuration__c sc2 = TestDataFactory.createSystemConfig('FC_v2');
        sc2.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        sc2.RegExp__c = '^[FC]([0-9]{2})((0[1-9])|(1[0-2]))[0-9]{5}$';
        sc2.Name = 'FC_v2';
        insert sc2;

        Test.startTest();
        //================开始测试================

        // 测试空品牌名
        Boolean result = WarrantyService.verifySerialNumber('', 'A1801000001', null);
        System.assertEquals(false, result, '测试空品牌名失败');

        result = WarrantyService.verifySerialNumber(null, 'A1801000001', null);
        System.assertEquals(false, result, '测试null品牌名失败');

        // 测试EGO品牌
        result = WarrantyService.verifySerialNumber('EGO', 'A1801000001', null);
        System.assertEquals(true, result, '测试EGO品牌SN验证失败');

        result = WarrantyService.verifySerialNumber('EGO', 'INVALID', null);
        System.assertEquals(false, result, '测试EGO品牌无效SN失败');

        // 测试特殊产品型号SC535801
        result = WarrantyService.verifySerialNumber('EGO', '420000001', 'SC535801');
        System.assertEquals(true, result, '测试SC535801特殊SN失败');

        result = WarrantyService.verifySerialNumber('EGO', '420015000', 'SC535801');
        System.assertEquals(true, result, '测试SC535801边界SN失败');

        result = WarrantyService.verifySerialNumber('EGO', '420015001', 'SC535801');
        System.assertEquals(false, result, '测试SC535801超出范围SN失败');

        result = WarrantyService.verifySerialNumber('EGO', '419999999', 'SC535801');
        System.assertEquals(false, result, '测试SC535801低于范围SN失败');

        // 测试特殊产品型号NA1800B-00
        result = WarrantyService.verifySerialNumber('EGO', '420000001', 'NA1800B-00');
        System.assertEquals(true, result, '测试NA1800B-00特殊SN失败');

        result = WarrantyService.verifySerialNumber('EGO', '420001848', 'NA1800B-00');
        System.assertEquals(true, result, '测试NA1800B-00边界SN失败');

        result = WarrantyService.verifySerialNumber('EGO', '420001849', 'NA1800B-00');
        System.assertEquals(false, result, '测试NA1800B-00超出范围SN失败');

        // 测试FC产品的特殊验证
        result = WarrantyService.verifySerialNumber('EGO', 'F1801000001', 'TEST-FC', false);
        System.assertEquals(true, result, '测试FC产品SN验证失败');

        result = WarrantyService.verifySerialNumber('EGO', 'A1801000001', 'TEST-FC', false);
        System.assertEquals(true, result, '测试FC产品EGO SN验证失败');

        result = WarrantyService.verifySerialNumber('EGO', 'INVALID', 'TEST-FC', false);
        System.assertEquals(false, result, '测试FC产品无效SN失败');

        // 测试编辑模式下的FC产品
        result = WarrantyService.verifySerialNumber('EGO', 'A1801000001', 'TEST-FC', true);
        System.assertEquals(true, result, '测试编辑模式FC产品失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试WarrantyService的静态查询方法 - 第一部分
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testStaticQueryMethods1(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        war.Name = 'TEST_WARRANTY_001';
        insert war;

        //4、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;
        wi1.ActualIndicator__c = 'Vailid Warranty';
        insert wi1;

        //5、创建Order数据
        Order ord = new Order();
        ord.AccountId = acc.Id;
        ord.EffectiveDate = Date.today();
        ord.Status = 'Draft';
        ord.Warranty__c = war.Id;
        insert ord;

        Test.startTest();
        //================开始测试================

        // 测试getWarrantyMapByIdSet
        Set<String> warrantyIdSet = new Set<String>{war.Id};
        Map<String, Warranty__c> warrantyMap = WarrantyService.getWarrantyMapByIdSet(warrantyIdSet);
        System.assertEquals(1, warrantyMap.size(), '测试getWarrantyMapByIdSet失败');
        System.assertEquals(war.Id, warrantyMap.get(war.Id).Id, '测试getWarrantyMapByIdSet返回数据失败');

        // 测试getWarrantyNoItemsMapByIdSet
        Map<String, Warranty__c> warrantyNoItemsMap = WarrantyService.getWarrantyNoItemsMapByIdSet(warrantyIdSet);
        System.assertEquals(1, warrantyNoItemsMap.size(), '测试getWarrantyNoItemsMapByIdSet失败');

        // 测试getWarrantyAndOrderById
        Warranty__c warrantyWithOrder = WarrantyService.getWarrantyAndOrderById(war.Id);
        System.assertNotEquals(null, warrantyWithOrder, '测试getWarrantyAndOrderById失败');
        System.assertEquals(1, warrantyWithOrder.Order__r.size(), '测试getWarrantyAndOrderById订单数量失败');

        // 测试getWarrantyAndOrderByIdSet
        List<Warranty__c> warrantyListWithOrder = WarrantyService.getWarrantyAndOrderByIdSet(warrantyIdSet);
        System.assertEquals(1, warrantyListWithOrder.size(), '测试getWarrantyAndOrderByIdSet失败');

        // 测试getWarrantyItemByWarrantyId
        List<Warranty_Item__c> warrantyItems = WarrantyService.getWarrantyItemByWarrantyId(war.Id);
        System.assertEquals(1, warrantyItems.size(), '测试getWarrantyItemByWarrantyId失败');

        // 测试getWarrantyByWarrantyIds
        Warranty__c warrantyById = WarrantyService.getWarrantyByWarrantyIds(war.Id);
        System.assertNotEquals(null, warrantyById, '测试getWarrantyByWarrantyIds失败');

        // 测试getWarrantyByWarrantyIds with null
        Warranty__c warrantyByNullId = WarrantyService.getWarrantyByWarrantyIds(null);
        System.assertNotEquals(null, warrantyByNullId, '测试getWarrantyByWarrantyIds null失败');

        // 测试getWarrantyByWarrantyName
        Warranty__c warrantyByName = WarrantyService.getWarrantyByWarrantyName(war.Name);
        System.assertEquals(war.Id, warrantyByName.Id, '测试getWarrantyByWarrantyName失败');

        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试覆盖率提升方法 - 覆盖剩余未测试的代码行
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到95%
     */
    static testMethod void testForCoverageImprovement(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();
        pro.THD_store_warranty_period__c = 365;
        pro.Amazon_Store_warranty_period__c = 180;
        pro.Dealer_Grainger_Store_warranty_period__c = 90;
        pro.Landed_Cost__c = 100;
        pro.Pick_up_Fee__c = 10;
        update pro;

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'Amazon';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Other';
        dp3.Name = 'Other';
        insert dp3;

        //4、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Purchase_Date__c = Date.today().addDays(-10);
        war.Master_Product__c = pro.Id;
        war.Brand_Name__c = 'EGO';
        war.Product_Use_Type2__c = 'Residential';
        insert war;

        //5、创建warranty item数据
        Warranty_Item__c wi1 = TestDataFactory.createWarrantyItem(war.Id);
        wi1.Warranty__c = war.Id;
        wi1.Product__c = pro.Id;
        wi1.Product_Type__c = 'Product';
        insert wi1;

        Test.startTest();
        //================开始测试================
        WarrantyService ws = new WarrantyService();
        ws.warranty = war;
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1};

        // 测试countCost方法 - Home Depot场景
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.countCost();
        System.assertNotEquals(null, ws.warranty.Store_Policy_Cost__c, '测试Home Depot费用计算失败');
        System.assertNotEquals(null, ws.warranty.Replacement_Cost__c, '测试Home Depot替换费用失败');
        System.assertNotEquals(null, ws.warranty.Recommendation_of_Warranty_Issue__c, '测试Home Depot建议失败');

        // 测试countCost方法 - Amazon场景
        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.warranty = war;
        ws.countCost();
        System.assertNotEquals(null, ws.warranty.Store_Policy_Cost__c, '测试Amazon费用计算失败');
        System.assertNotEquals(null, ws.warranty.Replacement_Cost__c, '测试Amazon替换费用失败');
        System.assertNotEquals(null, ws.warranty.Recommendation_of_Warranty_Issue__c, '测试Amazon建议失败');

        // 测试countCost方法 - Other场景
        war.Place_of_Purchase_picklist__c = 'Other';
        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.countCost();
        System.assertEquals(null, ws.warranty.Store_Policy_Cost__c, '测试Other费用计算失败');
        System.assertEquals(null, ws.warranty.Replacement_Cost__c, '测试Other替换费用失败');
        System.assertNotEquals(null, ws.warranty.Recommendation_of_Warranty_Issue__c, '测试Other建议失败');

        // 测试countCost方法 - Unauthorized Dealer场景
        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        ws.countCost();
        System.assertEquals('Return to place of purchase.', ws.warranty.Recommendation_of_Warranty_Issue__c, '测试Unauthorized Dealer建议失败');

        // 测试saveWarrantyItem方法 - 产品未变化场景
        ws.masterProductOld = war.Master_Product__c;
        ws.saveWarrantyItem();

        // 测试saveWarrantyItem方法 - 产品变化场景
        ws.masterProductOld = 'different_product_id';
        ws.warrantyItemOldList = new List<Warranty_Item__c>();
        wi1.Id = null; // 模拟新记录
        ws.warrantyItemList = new List<Warranty_Item__c>{wi1};
        ws.saveWarrantyItem();

        // 测试空warrantyItemList
        ws.warrantyItemList = new List<Warranty_Item__c>();
        ws.saveWarrantyItem(); // 应该直接返回

        Test.stopTest();
    }
}