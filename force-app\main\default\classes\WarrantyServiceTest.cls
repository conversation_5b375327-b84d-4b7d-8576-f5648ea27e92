/**
 * @Author:          Zack
 * @Date:            2018-02-23
 * @Description:
 * @Test_Class:      WarrantyService
 * @Related_Class:
 * @Last_Modified_by: <PERSON>
 * @Last_Modified_time: 2018-08-13 14:34:54
 * @Modifiy_Purpose:    WarrantyService发生更改，相应更改测试类
 */
@isTest
private class WarrantyServiceTest {
	/**
	 * @Author:     Zack
	 * @Date:       2018-02-23
	 * @Return:     
	 * @Function:   测试WarrantyService的构造方法、setStoreReturnExchangePolicy、setWarrantyItemExpirationDate等方法
	 * @Last_Modified_by:  Zack
 	 * @Last_Modified_time:2018-07-12
 	 * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
	 */
    static testMethod void testMethodsOne() {
    	//========初始化数据========
    	//1、创建Account数据
    	Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        Account_SyncToShopify obj = new Account_SyncToShopify();
        Account_SyncToShopify.functionName.add('Account');
        CCM_SharingUtil.isSharingOnly = true;
    	insert acc;

    	//2、创建Product数据
    	Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;
        
        System_Configuration__c sc = new System_Configuration__c();
        sc.Name = 'Claim Auto Approval';
        sc.Is_Active__c = true;
        insert sc;


    	//4、创建Warranty数据
    	Warranty__c war = TestDataFactory.createWarranty();
    	war.AccountCustomer__c = acc.Id;
    	war.Store_return_exchange_policy__c = Date.today().addDays(60);
    	war.Purchase_Date__c = Date.today();
    	war.Master_Product__c = pro.Id;
    	insert war;

		Test.startTest();
    	//5、创建warranty item数据
    	Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    	wi.Product_Model__c = '3';
    	wi.Warranty__c = war.Id;
    	// insert wi;
		Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
        wi2.Product_Type__c = 'Product';
        // insert wi2;
        Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
        // insert wi3;
        Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
        wi4.Product_Model__c = '3';
        wi4.Warranty__c = war.Id;
        // insert wi4;
        Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
        wi5.Warranty__c = war.Id;
        wi5.Product_Type__c = 'Product';
        // insert wi5;
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

    	List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    	wiList.add(wi);
        wiList.add(wi2);
        wiList.add(wi3);
        wiList.add(wi4);
    	wiList.add(wi5);
		insert wiList;
    	//6、创建SystemConfiguration
    	TestDataFactory.createSystemConfig('v3');
			
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //2、测试setStoreReturnExchangePolicy方法
        //  测试BrandName为skil的情况
        war.Brand_Name__c = 'Skil';
        /* war.Place_of_Purchase_picklist__c = 'Wal-mart';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        war.Place_of_Purchase_picklist__c = 'Menards';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        /*war.Place_of_Purchase_picklist__c = 'Ferguson';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();*/

        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //  测试BrandName为未知的情况
        war.Brand_Name__c = 'other';
        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.setStoreReturnExchangePolicy();

        //3、测试setWarrantyItemExpirationDate方法
        ws.warrantyItemList = wiList;
        //  测试Brand Name为Skil的情况
        war.Brand_Name__c = 'Skil';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        war.Place_of_Purchase_picklist__c = 'Authorized Dealer';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(true);

        //  测试Brand Name为SkilSaw的情况
        war.Brand_Name__c = 'SkilSaw';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        //  测试Brand Name为未知的情况
        war.Brand_Name__c = 'Unknown';
        war.Purchase_Date__c = null;
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        Test.stopTest();
        war.Purchase_Date__c = System.today();
        war.Product_Use_Type2__c = 'Residential';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);

        war.Product_Use_Type2__c = 'Professional/Commercial';
        ws.warranty = war;
        ws.setWarrantyItemExpirationDate(false);
        
        ws.setStoreReturnExchangePolicy();
        Attachment att = new Attachment();
        att.Body = Blob.valueOf( 'Testing' );
        att.Name = 'test att';
        att.ParentId = war.Id;
        insert att;
        ws.receiptAttachmentID = att.Id;
        ws.relateWarrantyAndAttachment();
    
        Set<String> warrantyIdSet = new Set<String>();
        warrantyIdSet.add(war.Id);
        WarrantyService.getWarrantyNoItemsMapByIdSet(warrantyIdSet);
        
    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    static testMethod void testMethodsOne2() {
        //========初始化数据========
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Price Book数据
        Default_PriceBook__c dp = new Default_PriceBook__c();
        dp.Place_of_Purchase__c = 'Home Depot';
        dp.Name = 'HomeDepot';
        insert dp;

        Default_PriceBook__c dp2 = new Default_PriceBook__c();
        dp2.Place_of_Purchase__c = 'Amazon LLC';
        dp2.Name = 'AmazonLLc';
        insert dp2;

        Default_PriceBook__c dp3 = new Default_PriceBook__c();
        dp3.Place_of_Purchase__c = 'Ferguson';
        dp3.Name = 'Ferguson';
        insert dp3;

        Default_PriceBook__c dp4 = new Default_PriceBook__c();
        dp4.Place_of_Purchase__c = 'Other';
        dp4.Name = 'Other';
        insert dp4;

        PricebookEntry pbe = new PricebookEntry();
        pbe.Pricebook2Id = Test.getStandardPricebookId();
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 66;
        insert pbe;


        //4、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //5、创建warranty item数据
        Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
        wi6.Warranty__c = war.Id;
        Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
        wi7.Warranty__c = war.Id;

        //6、创建SystemConfiguration
        TestDataFactory.createSystemConfig('v3');
		
        Test.startTest();
        //========开始测试========
        //1、测试构造方法
        WarrantyService ws = new WarrantyService();

        //4、测试countCost方法
        war.Place_of_Purchase_picklist__c = 'Unknown';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Home Depot';
        ws.warranty = war;
        ws.countCost();

        war.Place_of_Purchase_picklist__c = 'Amazon LLC';
        ws.warranty = war;
        ws.countCost();

        //5、测试saveWarrantyItem方法
        List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi6);
        ws.warrantyItemList = wiList2;
        ws.saveWarrantyItem();

        wiList2 = new List<Warranty_Item__c>();
        wiList2.add(wi7);
        ws.warrantyItemList = wiList2;
        ws.masterProductOld = war.Master_Product__r.Id;
        ws.saveWarrantyItem();

        //6、测试uploadReceiptAttachment方法
        ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

        //7、测试relateWarrantyAndAttachment方法
        //******
        //ws.relateWarrantyAndAttachment();
        Test.stopTest();

    }

    /**
     * @Author:     Zack
     * @Date:       2018-02-23
     * @Return:     
     * @Function:   测试WarrantyService的构造方法、countCost、
     *              saveWarrantyItem、uploadReceiptAttachment、relateWarrantyAndAttachment等方法
     * @Last_Modified_by:  Zack
     * @Last_Modified_time:2018-07-12
     * @Modifiy_Purpose:   WarrantyService发生更改，相应更改测试类
     */
    // static testMethod void testMethodsOne3() {
    //     //========初始化数据========
    //     //1、创建Account数据
    //     Account acc = TestDataFactory.createAccount();
    //     insert acc;

    //     //2、创建Product数据
    //     Product2 pro = TestDataFactory.createProduct();
    //     insert pro;

    //     //3、创建Price Book数据
    //     Default_PriceBook__c dp = new Default_PriceBook__c();
    //     dp.Place_of_Purchase__c = 'Home Depot';
    //     dp.Name = 'HomeDepot';
    //     insert dp;

    //     Default_PriceBook__c dp2 = new Default_PriceBook__c();
    //     dp2.Place_of_Purchase__c = 'Amazon LLC';
    //     dp2.Name = 'AmazonLLc';
    //     insert dp2;

    //     Default_PriceBook__c dp3 = new Default_PriceBook__c();
    //     dp3.Place_of_Purchase__c = 'Ferguson';
    //     dp3.Name = 'Ferguson';
    //     insert dp3;

    //     Default_PriceBook__c dp4 = new Default_PriceBook__c();
    //     dp4.Place_of_Purchase__c = 'Other';
    //     dp4.Name = 'Other';
    //     insert dp4;

    //     /*PricebookEntry pbe = new PricebookEntry();
    //     pbe.Pricebook2Id = dp.Id;
    //     pbe.Product2Id = pro.Id;
    //     pbe.UnitPrice = 66;
    //     insert pbe;

    //     PricebookEntry pbe2 = new PricebookEntry();
    //     pbe2.Pricebook2Id = dp2.Id;
    //     pbe2.Product2Id = pro.Id;
    //     pbe2.UnitPrice = 66;
    //     insert pbe2;

    //     PricebookEntry pbe3 = new PricebookEntry();
    //     pbe3.Pricebook2Id = dp3.Id;
    //     pbe3.Product2Id = pro.Id;
    //     pbe3.UnitPrice = 66;
    //     insert pbe3;*/

    //     //4、创建Warranty数据
    //     Warranty__c war = TestDataFactory.createWarranty();
    //     war.AccountCustomer__c = acc.Id;
    //     war.Store_return_exchange_policy__c = Date.today().addDays(60);
    //     war.Purchase_Date__c = Date.today();
    //     war.Master_Product__c = pro.Id;
    //     insert war;

    //     //5、创建warranty item数据
    //     Warranty_Item__c wi = TestDataFactory.createWarrantyItem(war.Id);
    //     wi.Product_Model__c = '3';
    //     wi.Warranty__c = war.Id;
    //     insert wi;
    //     Warranty_Item__c wi2 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi2.Product_Type__c = 'Product';
    //     insert wi2;
    //     Warranty_Item__c wi3 = TestDataFactory.createWarrantyItem(war.Id);
    //     insert wi3;
    //     Warranty_Item__c wi4 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi4.Product_Model__c = '3';
    //     wi4.Warranty__c = war.Id;
    //     insert wi4;
    //     Warranty_Item__c wi5 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi5.Warranty__c = war.Id;
    //     wi5.Product_Type__c = 'Product';
    //     insert wi5;
    //     Warranty_Item__c wi6 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi6.Warranty__c = war.Id;
    //     Warranty_Item__c wi7 = TestDataFactory.createWarrantyItem(war.Id);
    //     wi7.Warranty__c = war.Id;

    //     List<Warranty_Item__c> wiList = new List<Warranty_Item__c>();
    //     wiList.add(wi);
    //     wiList.add(wi2);
    //     wiList.add(wi3);
    //     wiList.add(wi4);
    //     wiList.add(wi5);

    //     //6、创建SystemConfiguration
    //     TestDataFactory.createSystemConfig('v3');

    //     //========开始测试========
    //     //1、测试构造方法
    //     WarrantyService ws = new WarrantyService();

    //     //4、测试countCost方法
    //     war.Place_of_Purchase_picklist__c = 'Unknown';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Unauthorized Dealer';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Home Depot';
    //     ws.warranty = war;
    //     ws.countCost();

    //     war.Place_of_Purchase_picklist__c = 'Amazon LLC';
    //     ws.warranty = war;
    //     ws.countCost();

    //     //5、测试saveWarrantyItem方法
    //     List<Warranty_Item__c> wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi6);
    //     ws.warrantyItemList = wiList2;
    //     ws.saveWarrantyItem();

    //     wiList2 = new List<Warranty_Item__c>();
    //     wiList2.add(wi7);
    //     ws.warrantyItemList = wiList2;
    //     ws.masterProductOld = war.Master_Product__r.Id;
    //     ws.saveWarrantyItem();

    //     //6、测试uploadReceiptAttachment方法
    //     ws.uploadReceiptAttachment(Blob.valueOf('ssssssss'), 'jepg');

    //     //7、测试relateWarrantyAndAttachment方法
    //     //******
    //     //ws.relateWarrantyAndAttachment();
    // }

    /**
     * @Author:     Zack
     * @Date:       2018-07-12
     * @Return:     
     * @Function:   测试WarrantyService的verifySerialNumber、getWarrantyByID、getWarrantySimpleByID、
     *              getWarrantyMapByIdSet、getWarrantyAndOrderById、getWarrantyAndOrderByIdSet、
     *              getWarrantyItemByWarrantyId、getWarrantyByWarrantyIds、getWarrantyByWarrantyName等方法
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   
     */
    static testMethod void testMethodsTwo(){
        //================初始化数据================
        //1、创建Account数据
        Account acc = TestDataFactory.createAccount();
        acc.TaxID__c = 'test';
        insert acc;

        //2、创建Product数据
        Product2 pro = TestDataFactory.createProduct();

        //3、创建Warranty数据
        Warranty__c war = TestDataFactory.createWarranty();
        war.AccountCustomer__c = acc.Id;
        war.Store_return_exchange_policy__c = Date.today().addDays(60);
        war.Purchase_Date__c = Date.today();
        war.Master_Product__c = pro.Id;
        insert war;

        //4、创建System Configuration数据
        System_Configuration__c sc = TestDataFactory.createSystemConfig('SN_Format');
        sc.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('System_Configuration__c', 'SN_Format');
        sc.RegExp__c = '^[ACU]([0-9]{2})((0[1-9])|(1[0-2]))[0-9]{5}$';
        sc.Name = 'EGO Skil SkilSaw';
        insert sc;
			
        Test.startTest();
        //================开始测试================
        //1、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('');
        WarrantyService.verifySerialNumber('NBM011506000001X');

        //2、测试verifySerialNumber方法
        WarrantyService.verifySerialNumber('EGO', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('Skil', 'NBM011506000001X', null);
        WarrantyService.verifySerialNumber('SkilSaw', 'NBM011506000001X', null);

        //3、测试getWarrantyByID方法
        WarrantyService.getWarrantyByID(war.Id);
        WarrantyService.getWarrantyByID('');

        //4、测试getWarrantySimpleByID方法
        WarrantyService.getWarrantySimpleByID(war.Id);
        WarrantyService.getWarrantySimpleByIDforCase(war.Id);
        WarrantyService.getWarrantySimpleByID('');

        //5、测试getWarrantyMapByIdSet方法
        Set<String> warIdSet = new Set<String>();
        warIdSet.add(war.Id);
        WarrantyService.getWarrantyMapByIdSet(warIdSet);

        //6、测试getWarrantyAndOrderById方法
        WarrantyService.getWarrantyAndOrderById(war.Id);

        //7、测试getWarrantyAndOrderByIdSet方法
        WarrantyService.getWarrantyAndOrderByIdSet(warIdSet);

        //8、测试getWarrantyItemByWarrantyId方法
        WarrantyService.getWarrantyItemByWarrantyId(war.Id);

        //9、测试getWarrantyByWarrantyIds方法
        WarrantyService.getWarrantyByWarrantyIds(war.Id);

        //10、测试getWarrantyByWarrantyName方法
        WarrantyService.getWarrantyByWarrantyName(war.Name);
        
        Test.stopTest();
    }
}