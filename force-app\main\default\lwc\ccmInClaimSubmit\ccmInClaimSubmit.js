/**
 * Created by eric on 2021/5/18.
**/

 import {LightningElement, api, track, wire} from 'lwc';
 import { CurrentPageReference } from 'lightning/navigation';
 import { NavigationMixin } from 'lightning/navigation';
 import { ShowToastEvent } from 'lightning/platformShowToastEvent';
 import { getObjectInfo } from "lightning/uiObjectInfoApi";
 import CoOpClaim_OBJECT from "@salesforce/schema/Co_Op_Claim__c";
 import initGetData from '@salesforce/apex/CCM_CoOpClaimCtl.initGetData';
 import saveClaimAndClaimItem from '@salesforce/apex/CCM_CoOpClaimCtl.saveClaimAndClaimItem';
 import saveDeductionInvoice from '@salesforce/apex/CCM_CoOpClaimCtl.saveDeductionInvoice';
 import deleteContentDocument from "@salesforce/apex/CCM_CoOpClaimCtl.deleteContentDocument";

 import getAddress from "@salesforce/apex/CCM_CoOpClaimCtl.getAddress";
 import getAvailabelProgram from "@salesforce/apex/CCM_CoOpClaimCtl.getAvailabelProgram";
 import getAvailabelInvoice from "@salesforce/apex/CCM_CoOpClaimCtl.getAvailabelInvoice";
 import submitForApproval from "@salesforce/apex/CCM_CoOpUtil.submitForApproval";

 import getCustomer from "@salesforce/apex/CCM_CoOpUtil.getCustomer";

 export default class CcmInClaimSubmit extends NavigationMixin(LightningElement) {
     showSpinner = false;
     @api coOpProgramId = '';//program id
     connected = false;

     realtimeSearchCustomer = true;
     @track
     selectedCustomer = {};//selected customer
     @track
     customerSource = [];//customer source

     @track
     selectedProgram = {};//selected program
     @track
     programSource = [];//program source

     mediaOptions = [];//media options
     @track
     brandOptions = [];//brand options
     @track
     categoryOptions = [];//category options
     @track
     is2ndTierDealer = false;

     @track
     isCanada = false;

     @track
     paymentOptions =[];//payment options

     //claim data
     @track
     claimData = {
         Customer__c : '',
         Co_Op_Program__c : '',
         Bill_To__c : '',
         Billing_Address__c:'',
         Ship_To__c: '',
         Shipping_Address__c:'',
         Comment__c: ''
     };
     customerName = '';//customer name
     programName ='';//program name
     remainingFunds=0;//remaining funds
     totalCoOpFunds=0;//total funds
     @track
     itemsTake = [];//claim items
     @track
     deleteItemIds = [];//deleted items
     @track
     totalClaimAmount = 0;//total claim amount
     @track
     totalClaimGST = 0;
     @track
     totalClaimQST = 0;
     @track
     totalClaimHST = 0;

     approvalComments = '';

     @track
     billingAddressSource = [];
     @track
     selectedBillingAddress = {};

     @track
     shippingAddressSource = [];
     @track
     selectedShippingAddress = {};

     showUploadModal = false;
     showPicturesModal = false;
     showApprovalCommentsModal = false;
     @track
     selectedItemUploadedFiles = [];

     @track
     invoiceItems = [];
     @track
     invoiceSource = [];

     get summaryTotalAmount() {
        return this.totalClaimAmount + this.totalClaimGST + this.totalClaimHST + this.totalClaimQST;
    }

     get acceptedFormats() {
         return ['.pdf', '.png', '.jpg', '.jpeg', '.ppt', '.pptx', '.xlsx', '.xls', '.csv', '.msg','.doc','.docx','.mp4','.mp3'];
     }

     @track
     steps = [
         { label: "Create Claim", value: 1 },
         { label: "Upload Files", value: 2 }
     ];

     currentStep = 1;

     get firstStep(){
         return this.currentStep === 1;
     }

     get secondStep(){
         return this.currentStep === 2;
     }

     get isDeduction(){
         return this.claimRecordTypeId && this.claimRecordTypes[this.claimRecordTypeId].name === 'Deduction';
     }

     @track
     remainingFunds = 0;
     @track
     totalCoOpFunds = 0;

     @wire(CurrentPageReference)
     setCurrentPageReference(currentPageReference) {
         if (currentPageReference.state) {
             const _state = currentPageReference.state;
             this.claimid = _state.c__claimid;
         }
     }

     //initial page
     connectedCallback(){
         console.log(this.claimid);
         if(!this.connected){
             if(!this.claimid){
                 this.itemsTake.push({
                     lineNo: this.itemsTake.length+1,
                     Media_Type__c : '',
                     Targeted_Brand__c : '',
                     Targeted_Product_Category__c : '',
                     Expected_Outcome_Description__c: '',
                     Ad_Time_From__c: undefined,
                     Ad_Time_To__c : undefined,
                     Targeted_Brands__c : '',
                     Targeted_Product_Categories__c : '',
                     selectedTargetedBrand : [],
                     GST__c : 0,
                     HST__c : 0,
                     QST__c : 0,
                     selectedTargetedProductCategory : [],
                     uploadedFilesNumber : 0
                 });
             } else {
            //Get initial data by claim id
            initGetData({claimId: this.claimid}).then(result => {
                if(result){
                    let data = JSON.parse(result);
                    if (data){
                        this.mediaOptions = data.mediaTypeValue;
                        this.brandOptions = data.targetedBrandValue ? data.targetedBrandValue : [];
                        this.categoryOptions = data.productCategoryValue ? data.productCategoryValue : [];
                        this.selectedCustomer = {"label" : data.customerName, "value" : data.customerId};
                        this.selectedProgram = {"programName" : data.programName, "programId" : data.programId};
                        this.isCanada = data.orgCode === 'CCA';

                        this.is2ndTierDealer = data.is2ndTierDealer;
                        this.remainingFunds = data.remainingFunds;
                        this.totalCoOpFunds = data.totalCoOpFunds;

                        if(data.fundingBasedOn && data.fundingBasedOn == 'Current Year POS'){
                            this.isPOS = true;
                        }else{
                            this.isPOS = false;
                        }

                        // this.invoiceItems = data.deductionList;
                        if(data.deductionList) {
                            var index = 1;
                            data.deductionList.forEach(di => {
                                this.invoiceItems.push(
                                    {
                                        "lineNo": index,
                                        "recordId" : di.Id,
                                        "invoiceName" : di.Invoice__c?di.Invoice__r.Invoice_Number__c:'',
                                        "invoiceId" : di.Invoice__c,
                                        "amount" : di.Deduction_Amount__c
                                });
                                index ++;
                            })
                        }

                        if(this.claimid){
                            this.claimData = data.coOpclaim;
                            this.claimData.claimName = data.claimName;
                            this.claimData.claimStatus = data.claimStatus;
                            this.itemsTake = data.coOpClaimItemList;
                            if(data.relatedFiles && data.relatedFiles.length > 0){
                                this.relatedFiles = data.relatedFiles;
                            }

                            this.itemsTake.forEach(e => {
                                e.requestAmount =  Number(e.Claim_Amount__c) + Number(e.GST__c) + Number(e.HST__c) + Number(e.QST__c);
                                e.Reimbursement_Rate__c = e.Reimbursement_Rate__c / 100;
                                if (e.Targeted_Brands__c) {
                                    e.selectedTargetedBrand = this.brandOptions.filter(t => e.Targeted_Brands__c.split(";").findIndex( item => item === t.value) > -1);
                                } else {
                                    e.selectedTargetedBrand = [];
                                }

                                if (e.Targeted_Product_Categories__c) {
                                    e.selectedTargetedProductCategory = this.categoryOptions.filter(t => e.Targeted_Product_Categories__c.split(";").findIndex( item => item === t.value) > -1);
                                } else {
                                    e.selectedTargetedProductCategory = [];
                                }

                                if(this.relatedFiles){
                                    let i = this.relatedFiles.find(r=>r.Id == e.Id);
                                    if(i){
                                        e.uploadedFilesNumber ++;
                                    }
                                }

                            });

                            getAvailabelProgram({customerId: data.customerId}).then((result)=>{
                                if(result){
                                    this.programSource = result;
                                    let _selected = this.programSource.find(item => item.programId === data.programId);
                                    this.paymentOptions = [];
                                    if(_selected.paymentTypes){
                                        _selected.paymentTypes.forEach(item=>{
                                            this.paymentOptions.push({
                                                label: item,
                                                value: Object.keys(this.claimRecordTypes).find((rti) => this.claimRecordTypes[rti].name === item)
                                            });
                                        });
                                    }
                                    if (this.paymentOptions) {
                                        this.paymentOptions.forEach(opt => {
                                            if(opt.label === data.claimRecordType) {
                                                this.claimRecordTypeId = opt.value;
                                            }
                                        });
                                    }
                                }else{
                                    this.dispatchEvent(
                                        new ShowToastEvent({
                                            title: 'Error',
                                            message: 'There is no available program for the customer.',
                                            variant: 'error'
                                        })
                                    );
                                }
                            },error=>{
                                console.error(error);
                                this.dispatchEvent(
                                    new ShowToastEvent({
                                        title: 'Error',
                                        message: error.body.message,
                                        variant: 'error'
                                    })
                                );
                            });

                            this.getCustomerAddress(data.customerId, data.programId);
                            this.getCustomerInvoice(data.customerId);

                        }else{
                            this.claimData.Customer__c = data.customerId;
                            this.claimData.Co_Op_Program__c = this.programid;
                        }
                    }
                }
            }, error =>{
                console.error(error);
                this.dispatchEvent(
                    new ShowToastEvent({
                        title: 'Error',
                        message: error.body.message,
                        variant: 'error'
                    })
                );
            });
        }

        this.connected = true;
    }
}

     //Add claim item
     handleClickAddItemShortage(event){
         this.itemsTake.push({
             lineNo: this.itemsTake.length+1,
             Media_Type__c : '',
             Targeted_Brand__c : '',
             Targeted_Product_Category__c : '',
             Expected_Outcome_Description__c: '',
             Ad_Time_From__c: undefined,
             Ad_Time_To__c : undefined,
             GST__c : 0,
             HST__c : 0,
             QST__c : 0,
             selectedTargetedBrand : [],
             selectedTargetedProductCategory : [],
             uploadedFilesNumber : 0
         });
     }

     //Delete Claim item
     handleDeleteShortageItem(event){
         let _index = parseInt(event.currentTarget.dataset.id,10);
         console.log(_index);
         let deleteItems = this.itemsTake.splice(_index,1);
         this.itemsTake.forEach((item,index)=>{
             item.lineNo = index + 1;
         });
         if(this.claimData.Id && deleteItems[0].Id){
             this.deleteItemIds.push(deleteItems[0].Id);
         }
     }

     //change claim property.
     handleClaimChange(event){
         let _name = event.currentTarget.name;
         if(_name === 'invoiceNumber'){
             this.claimData.Reference_Invoice_Number__c = event.currentTarget.value.trim();
         }
     }

     //change claim item
     handleClaimItemChange(event){
    let _name = event.currentTarget.name;
    let _index = parseInt(event.currentTarget.dataset.id,10);
    if (_name === 'media') {
        this.itemsTake[_index].Media_Type__c = event.detail.value;
    } else if(_name === 'brand') {
        this.itemsTake[_index].Targeted_Brand__c = event.detail.value;
    } else if(_name === 'category') {
        this.itemsTake[_index].Targeted_Product_Category__c = event.detail.value;
    } else if(_name === 'adStartDate') {
        this.itemsTake[_index].Ad_Time_From__c = event.detail.value;
    } else if(_name === 'adEndDate') {
        this.itemsTake[_index].Ad_Time_To__c = event.detail.value;
    } else if(_name === 'claimAmount') {
        this.itemsTake[_index].Claim_Amount__c = event.currentTarget.value;
    } else if (_name === 'GST') {
        this.itemsTake[_index].GST__c = event.currentTarget.value;
    } else if (_name === 'HST') {
        this.itemsTake[_index].HST__c = event.currentTarget.value;
    } else if (_name === 'QST') {
        this.itemsTake[_index].QST__c = event.currentTarget.value;
    } else if(_name === 'description'){
        this.itemsTake[_index].Expected_Outcome_Description__c = event.currentTarget.value;
    }
    this.itemsTake[_index].requestAmount =  Number(this.itemsTake[_index].Claim_Amount__c) + Number(this.itemsTake[_index].GST__c) + Number(this.itemsTake[_index].HST__c) + Number(this.itemsTake[_index].QST__c);
     }

     //save claim and claim item
     handleNext(event){
         let _haveItems = this.itemsTake;

         if (_haveItems.length == 0) {
             this.showSpinner = false;
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Claim Item Required',
                     message: 'Please add claim Item before save!',
                     variant: 'warning'
                 })
             );
         }
         let _validate = this.validateBeforeSave();
         if(_validate && _haveItems.length > 0){
             this.claimData.Billing_Address__c = this.selectedBillingAddress.addressId;
             this.claimData.Shipping_Address__c = this.selectedShippingAddress.addressId;
             this.claimData.RecordTypeId = this.claimRecordTypeId;
             if(!this.claimData.Id){
                 this.claimData.Claim_Status__c = 'Draft';
             }

             let _index = 0;
             this.template.querySelectorAll('.targetedProductCategory').forEach(targetProductCategory => {
                 let _categories = [];
                 targetProductCategory.selectedList.forEach(category => {
                     _categories.push(category.value);
                 });
                 this.itemsTake[_index].Targeted_Product_Categories__c = _categories.join(";");

                 _index ++;
             });

             _index = 0;
             this.template.querySelectorAll('.targetedBrand').forEach(targetedBrand => {
                 let _brands = [];
                 targetedBrand.selectedList.forEach(brand => {
                     _brands.push(brand.value);
                 });
                 this.itemsTake[_index].Targeted_Brands__c = _brands.join(";");
                 _index ++;
             });
             this.itemsTake.forEach(e => {
                 e.selectedTargetedBrand = [];
                 e.selectedTargetedProductCategory = [];
             });
             console.log(this.itemsTake);

             let _saveData = {
                 coOpclaim : this.claimData,
                 coOpClaimItemList: this.itemsTake,
                 deleteClaimItemIds: this.deleteItemIds
             };
             this.showSpinner = true;
             saveClaimAndClaimItem({claimDataStr: JSON.stringify(_saveData)}).then(result=>{
                 if(result && result.isSuccess && result.returnData){
                     this.showSpinner = false;
                     this.claimData = result.returnData.coOpclaim;
                     if(this.claimData.Id){
                         this.claimData.Co_Op_Program_Customer__c = undefined;
                     }
                     this.claimData.claimName = result.returnData.claimName;
                     this.claimData.claimStatus = result.returnData.claimStatus;
                     this.itemsTake = result.returnData.coOpClaimItemList;

                     this.itemsTake.forEach(e=>{
                    e.requestAmount = Number(e.Claim_Amount__c) + Number(e.GST__c) + Number(e.HST__c) + Number(e.QST__c);
                    e.uploadedFilesNumber = 0;
                    e.Reimbursement_Rate__c = e.Reimbursement_Rate__c / 100;
                    if (this.isCanada) {
                        e.requestAmountExcludeTax = Number(e.Claim_Amount__c);
                        e.Reimbursement_Amount__c = e.requestAmountExcludeTax * e.Reimbursement_Rate__c;
                        e.Claim_GST__c = e.GST__c * e.Reimbursement_Rate__c;
                        e.Claim_QST__c = e.QST__c * e.Reimbursement_Rate__c;
                        e.Claim_HST__c = e.HST__c * e.Reimbursement_Rate__c;
                        this.totalClaimGST += Number(e.Claim_GST__c);
                        this.totalClaimQST += Number(e.Claim_QST__c);
                        this.totalClaimHST += Number(e.Claim_HST__c);
                    }
                    if(this.relatedFiles){
                        let i = this.relatedFiles.find(r=>r.Id == e.Id);
                        if (i) {
                            e.uploadedFilesNumber = i.uploadedFiles.length;
                        }
                        if(!i){
                            this.relatedFiles.push({
                                Id: e.Id,
                                uploadedFiles: []
                            });
                        }
                    }
                });

                     this.itemsTake.forEach(e => {
                         e.selectedTargetedBrand = this.brandOptions.filter(t => e.Targeted_Brands__c.split(";").findIndex( item => item === t.value) > -1);
                         console.log(e.selectedTargetedBrand);
                         e.selectedTargetedProductCategory = this.categoryOptions.filter(t => e.Targeted_Product_Categories__c.split(";").findIndex( item => item === t.value) > -1);
                     });
                     console.log(this.itemsTake);

                     this.currentStep = 2;
                     this.totalClaimAmount = 0;
                     if(this.itemsTake && this.itemsTake.length > 0){
                         this.itemsTake.forEach((element,index) => {
                             if(element.Claim_Amount__c > 0){
                                 this.totalClaimAmount += Number(element.Reimbursement_Amount__c);
                             }
                             if(element.Id){
                                 element.Co_Op_Claim__c = undefined;
                             }

                             element.lineNo = index + 1;
                         });
                     }
                     this.deleteItemIds=[];
                 }else{
                     this.showSpinner = false;
                     this.dispatchEvent(
                         new ShowToastEvent({
                             title: 'Error',
                             message: result.errorMsg,
                             variant: 'error'
                         })
                     );
                 }
             }, error=>{
                 console.error(error);
                 this.showSpinner = false;
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: error.body.message,
                         variant: 'error'
                     })
                 );
             });
         }
     }

     //back to previous step
     handleBack(event){
         this.currentStep = 1;
     }

     //submit co-op program claim
     handleSubmit(event){
         let _haveInvoiceItems = this.invoiceItems.length > 0;

         let _recordId = this.claimData.Id;
         // let _comments = this.claimData.Comment__c;
         let _comments = this.approvalComments;
         if(this.isDeduction){
             if (!_haveInvoiceItems) {
                 this.showSpinner = false;
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Deduction Invoice Required',
                         message: 'Please add Deduction Invoice first!',
                         variant: 'warning'
                     })
                 );
             }
             let _validateDIAmount = this.validateDeductionInvoice();
             let _validate = this.validateBeforeSave();
             if(_validate && _haveInvoiceItems && _validateDIAmount){
                 this.saveDeductionInvoice();
             }else{
                 return;
             }
         }
         this.showSpinner = true;
         submitForApproval({recordId: _recordId, comments: _comments}).then(result=>{
             if(result && result.isSuccess){
                 this.showSpinner = false;
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Success',
                         message: "The Co-Op Program Claim has been submitted successfully.",
                         variant: 'success'
                     })
                 );
                 this.currentStep = 1;
                 this.showApprovalCommentsModal = false;
                 // setTimeout(()=>{
                 //     this[NavigationMixin.Navigate]({
                 //         type: 'standard__recordPage',
                 //         attributes: {
                 //             recordId: _recordId,
                 //             objectApiName: 'Co_Op_Claim__c',
                 //             actionName: 'view'
                 //         }
                 //     });
                 // },500);
                 setTimeout(()=>{
                     // window.close();
                     // let url = "/s/co-op-program-detail?recordId=" + this.programid + "&isClaims=2";
                     let url = "/lightning/r/Co_Op_Program__c/" + _recordId + "/view";
                     window.open(url, "_self");
                 },1000);
             }else{
                 this.showSpinner = false;
                 this.showApprovalCommentsModal = false;
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: result.errorMessage,
                         variant: 'error'
                     })
                 );
             }
         },error=>{
             console.error(error);
             this.showSpinner = false;
             this.showApprovalCommentsModal = false;
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: error.body.message,
                     variant: 'error'
                 })
             );
         });
     }

     @wire(getObjectInfo, {
         objectApiName: CoOpClaim_OBJECT
     }) getobjectInfo(result) {
         if (result.data) {
             const rtis = result.data.recordTypeInfos;
             this.claimRecordTypes = rtis;
             //this.claimRecordTypeId = Object.keys(this.claimRecordTypes).find((rti) => this.claimRecordTypes[rti].name === 'Credit Memo');
             //console.log('RecordTypeId:' + this.claimRecordTypeId);
         }
     }

     //Get customer address
     getCustomerAddress(customerId, programId){
         getAddress({customerId: customerId, programId : programId}).then(result=>{
             console.log(result);
             if(result.isSuccess){
                 if(result.returnData && result.returnData.billing){
                     this.billingAddressSource = result.returnData.billing;
                     this.billingAddressSource.forEach(item=>{
                         let _str = '';
                         _str += item.country ? item.country + '  ' : '';
                         _str += item.state ? item.state + '  ' : '';
                         _str += item.city ? item.city + '  ' : '';
                         _str += item.address1 ? item.address1 + '  ' : '';
                         _str += item.address2 ? item.address2: '';
                         item.addressStr = _str.trim();
                         item.street = item.address1 ? item.address1 + ' ' :'' + item.address2 ? item.address2 :'';
                     });
                     if(this.claimData.Billing_Address__c){
                         this.selectedBillingAddress = this.billingAddressSource.find(e=>e.addressId == this.claimData.Billing_Address__c);
                     }
                 }
                 if(result.returnData && result.returnData.shipping && result.returnData.shipping.length > 0){
                     this.shippingAddressSource = result.returnData.shipping;
                     this.shippingAddressSource.forEach(item=>{
                         let _str = '';
                         _str += item.country ? item.country + '  ' : '';
                         _str += item.state ? item.state + '  ' : '';
                         _str += item.city ? item.city + '  ' : '';
                         _str += item.address1 ? item.address1 + '  ' : '';
                         _str += item.address2 ? item.address2: '';
                         item.addressStr = _str.trim();
                         item.street = _str.trim();
                         item.street = item.address1 ? item.address1 + ' ' :'' + item.address2 ? item.address2 :'';
                     });
                     if(this.claimData.Shipping_Address__c){
                         this.selectedShippingAddress = this.shippingAddressSource.find(e=>e.addressId == this.claimData.Shipping_Address__c);
                     }
                 }
             }else{
                 console.error(result.errorMsg);
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: result.errorMsg,
                         variant: 'error'
                     })
                 );
             }
         })
     }

     //Select billing and shipping address.
     handleAddressSelected(event){
         let _type = event.currentTarget.dataset.type;
         if(_type === "billing"){
             this.selectedBillingAddress = JSON.parse(event.detail);
         }
         if(_type === "shipping"){
             this.selectedShippingAddress = JSON.parse(event.detail);
             this.isAlternativeAddress = false;
         }
     }

     @track
     relatedFiles = [];

     //upload image
     handleItemUploadImage(event){
         this.showUploadModal = true;
         this.selectedClaimItemId = event.currentTarget.dataset.recordid;
         let files = [];
         let _claimItem = this.relatedFiles.find(item=>item.Id === this.selectedClaimItemId);
         if(_claimItem && _claimItem.uploadedFiles){
             files = files.concat(_claimItem.uploadedFiles)
         }

         this.selectedItemUploadedFiles = files;
     }

     //handle upload finished event
     handleUploadFinished(event){
         let _uploadedFiles = event.detail.files;
         let files = [];
         let _claimItem = this.relatedFiles.find(item=>item.Id === this.selectedClaimItemId);
         if(_claimItem && _claimItem.uploadedFiles){
             files = _claimItem.uploadedFiles.concat(_uploadedFiles);
         }else{
             files = _uploadedFiles;
         }
         files.forEach((item,index)=>{
             item.lineNo = index + 1;
         })
         _claimItem.uploadedFiles = files;

         this.selectedItemUploadedFiles = files;

         let _index = 0;
         for(_index = 0; _index < this.itemsTake.length; _index++) {
             if (this.itemsTake[_index].Id === this.selectedClaimItemId) {
                 console.log(this.itemsTake[_index].uploadedFilesNumber);
                 this.itemsTake[_index].uploadedFilesNumber = files.length;
                 console.log(this.itemsTake[_index].uploadedFilesNumber);
             }
         }
         console.log(this.itemsTake);
     }

     //delete upload files
     handleDeleteFile(event){
         let _index = parseInt(event.currentTarget.dataset.index,10);
         this.selectedItemUploadedFiles.splice(_index,1);
         let _docid = event.currentTarget.dataset.docid;
         deleteContentDocument({cdId: _docid}).then((data)=>{
             this.selectedItemUploadedFiles.forEach((item,index)=>{
                 item.lineNo = index + 1;
             })
             let _claimItem = this.relatedFiles.find(item=>item.Id === this.selectedClaimItemId);
             _claimItem.uploadedFiles = JSON.parse(JSON.stringify(this.selectedItemUploadedFiles));

             let _index = 0;
             for(_index = 0; _index < this.itemsTake.length; _index++) {
                 if (this.itemsTake[_index].Id === this.selectedClaimItemId) {
                     console.log(this.itemsTake[_index].uploadedFilesNumber);
                     this.itemsTake[_index].uploadedFilesNumber = this.selectedItemUploadedFiles.length;
                     console.log(this.itemsTake[_index].uploadedFilesNumber);
                 }
             }
         },error=>{
             console.error(error);
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: error.body.message,
                     variant: 'error'
                 })
             );
         })
     }

     //close upload form
     handleClickClose(){
         this.showUploadModal = false;
     }

     //close upload file form
     handleClickClosePictruesModel(){
         this.showPicturesModal = false;
     }


     handleCustomerSearchInputChange(event){
         if(event.detail){
             getCustomer({keyWord:event.detail}).then((result)=>{
                 if(result){
                     let _customers = [];
                     _customers = result.map(item=>{
                         return {
                             label: item.Name,
                             AccountNumber: item.AccountNumber?item.AccountNumber:item.Name,
                             value: item.Id,
                             type: item.Type,
                             orgCode: item.orgCode
                         }

                     });
                     this.customerSource = _customers;
                 }
             },error=>{
                 console.error(error);
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: error.body.message,
                         variant: 'error'
                     })
                 );
             })
         }else{
             this.selectedCustomer = {};
             this.selectedProgram = {};
             this.programSource=[];
             this.paymentOptions = [];
             this.selectedShippingAddress = {};
             this.selectedBillingAddress = {};
             this.shippingAddressSource = [];
             this.billingAddressSource = [];
             this.claimRecordTypeId = undefined;
             this.remainingFunds = 0;
             this.totalCoOpFunds = 0;
         }
     }

     //Select customer
     handleCustomerSelected(event){
         let customerSource = [...this.customerSource];
         let _selected = JSON.parse(event.detail);

         this.selectedCustomer = customerSource.find(item=>item.value===_selected.value);
         this.customerId = this.selectedCustomer.value;
         this.isCanada = this.selectedCustomer.orgCode === 'CCA';
         // this.getCustomerAddress(this.customerId);
         this.getCustomerProgram(this.customerId);
         this.getCustomerInvoice(this.customerId);
     }

     //Get program data by customer id
     getCustomerProgram(customerId){
         getAvailabelProgram({customerId: customerId}).then((result)=>{
             if(result){
                 this.programSource = result;
                 this.programSource.forEach(p =>{
                     p.label = p.programName + ' - ' + p.recordTypeName + ' Program';
                 });
             }else{
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: 'There is no available program for the customer.',
                         variant: 'error'
                     })
                 );
             }
         },error=>{
             console.error(error);
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: error.body.message,
                     variant: 'error'
                 })
             );
         });
     }

     //Get invoice by customer id
     getCustomerInvoice(customerId){
         getAvailabelInvoice({customerId: customerId}).then((result)=>{
             if(result){
                 this.invoiceSource = result;
                 this.invoiceSource.forEach(i =>{
                     i.label = i.invoiceName + ' - ' + i.invoiceStatus + ' - ' + i.invoiceType;
                 });
             }
         },error=>{
             console.error(error);
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: error.body.message,
                     variant: 'error'
                 })
             );
         });
     }

     //Select co-op program
     handleProgramSelected(event){
         let _selected = JSON.parse(event.detail);
         this.selectedProgram = this.programSource.find(item=>item.programId === _selected.programId);
         if(this.programId != _selected.programId) {
             this.selectedShippingAddress = {};
         }
         this.programId = _selected.programId;
         this.paymentOptions = [];
         this.claimRecordTypeId = undefined;
         this.remainingFunds = 0;
         this.totalCoOpFunds = 0;
         if(_selected.paymentTypes){
             _selected.paymentTypes.forEach(item=>{
                 this.paymentOptions.push({
                     label: item,
                     value: Object.keys(this.claimRecordTypes).find((rti) => this.claimRecordTypes[rti].name === item)
                 });
             });
         }

         initGetData({programId: this.programId, customerId: this.customerId}).then(result => {
             if(result){
                 let data = JSON.parse(result);
                 if(data){
                     this.mediaOptions = data.mediaTypeValue;
                     this.brandOptions = data.targetedBrandValue ? data.targetedBrandValue : [];
                     this.categoryOptions = data.productCategoryValue ? data.productCategoryValue : [];

                     this.is2ndTierDealer = data.is2ndTierDealer;
                     this.remainingFunds = data.remainingFunds;
                     this.totalCoOpFunds = data.totalCoOpFunds;

                     if(data.fundingBasedOn && data.fundingBasedOn == 'Current Year POS'){
                         this.isPOS = true;
                     }else{
                         this.isPOS = false;
                     }

                     if(this.claimid){
                         this.claimData = data.coOpclaim;
                         this.claimData.claimName = data.claimName;
                         this.claimData.claimStatus = data.claimStatus;
                         this.itemsTake = data.coOpClaimItemList;
                         if(data.relatedFiles && data.relatedFiles.length > 0){
                             this.relatedFiles = data.relatedFiles;
                         }

                     }else{
                         this.claimData.Customer__c = this.customerId;
                         this.claimData.Co_Op_Program__c = this.programId;
                     }
                 }
             }
         }, error =>{
             console.error(error);
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: error.body.message,
                     variant: 'error'
                 })
             );
         });

         this.getCustomerAddress(this.customerId, this.programId);

     }

     //Add invoice item
     handlePaymentChange(event){
         this.claimRecordTypeId = event.detail.value;
         let _paymentType = event.target.options.find(i=>i.value==event.detail.value).label;
         if(_paymentType === 'Deduction'){
             if(this.invoiceItems.length == 0){
                 this.invoiceItems.push({ lineNo:1});
             }
         }else{
             this.invoiceItems = [];
         }
         console.log('RecordTypeId:' + this.claimRecordTypeId);
     }

     //Validate claim data before saving.
     validateBeforeSave(){
         let _validate = true;

         this.template.querySelectorAll('lightning-combobox').forEach((item) => {
             if (!item.reportValidity()) {
                 _validate = false;
             }
         })
         this.template.querySelectorAll('lightning-input').forEach((item) => {
             if (!item.reportValidity()) {
                 _validate = false;
             }
         })
         this.template.querySelectorAll('c-ccm-autocomplete').forEach((item) => {
             if (!item.reportValidity()) {
                 _validate = false;
             }
         })
         if(!this.selectedBillingAddress || JSON.stringify(this.selectedBillingAddress) == "{}"){
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Billing Address Required',
                     message: "Please select an address!",
                     variant: 'warning'
                 })
             );
             _validate = false;
         }
         if (!this.is2ndTierDealer) {
             return _validate;
         }
        if(!this.selectedShippingAddress || JSON.stringify(this.selectedShippingAddress) == "{}"){
            this.dispatchEvent(
                new ShowToastEvent({
                    title: 'Shipping Address Required',
                    message: "Please select an address!",
                    variant: 'warning'
                })
            );
            _validate = false;
        }
         return _validate;
     }

     //Add invoice item
     handleAddInvoiceItem(){
         this.invoiceItems.push({lineNo: this.invoiceItems.length+1});
     }

     //Select invoice item
     handleInvoiceSelected(event){
         let _selected = JSON.parse(event.detail);
         let _index = parseInt(event.currentTarget.dataset.index,10);
         this.invoiceItems[_index].invoiceId = _selected.invoiceId;
         this.invoiceItems[_index].invoiceName = _selected.invoiceName;
     }

     //Change invoice amount
     handleInvoiceAmountChange(event){
         // let _amount = parseInt(event.detail.value,10);
         let _amount = Number(event.currentTarget.value);
         let _index = parseInt(event.currentTarget.dataset.index,10);
         this.invoiceItems[_index].amount = _amount;
     }

     //Delete invoice item
     @track
     deleteInvoiceIds = [];
     handleDeleteInvoiceItem(event){
         let _index = parseInt(event.currentTarget.dataset.index,10);
         let deleteItems = this.invoiceItems.splice(_index,1);
         this.invoiceItems.forEach((item,index)=>{
             item.lineNo = index + 1;
         });
         if(this.claimData.Id && deleteItems[0].recordId){
             this.deleteInvoiceIds.push(deleteItems[0].recordId);
         }
     }

     //save invoice items
     handleSave(){
         let _haveInvoiceItems = this.invoiceItems.length > 0;
         if (!_haveInvoiceItems) {
             this.showSpinner = false;
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Deduction Invoice Required',
                     message: 'Please add Deduction Invoice first!',
                     variant: 'warning'
                 })
             );
         }

         // let _validate = this.validateDeductionInvoice();
         // if(_validate && _haveInvoiceItems){
         //     this.saveDeductionInvoice();
         // }
         if(_haveInvoiceItems){
             this.saveDeductionInvoice();
         }
     }

     //save decution invoice data
     saveDeductionInvoice(){
        this.showSpinner = true;
         saveDeductionInvoice({claimId : this.claimData.Id, deductionInvoices: this.invoiceItems, deleteDeductionIds: this.deleteInvoiceIds}).then(
             result=>{
                 if(result && result.isSuccess && result.returnData){
                     this.showSpinner = false;
                     this.invoiceItems = result.returnData;
                     if(this.invoiceItems && this.invoiceItems.length > 0){
                         this.invoiceItems.forEach((item,index)=>{
                             item.lineNo = index + 1;
                         });
                     }
                     this.deleteInvoiceIds = [];
                     this.dispatchEvent(
                         new ShowToastEvent({
                             title: 'Success',
                             message: "Deduction invoices have been saved successfully.",
                             variant: 'success'
                         })
                     );
                 }else{
                     this.showSpinner = false;
                     this.dispatchEvent(
                         new ShowToastEvent({
                             title: 'Error',
                             message: result.errorMsg,
                             variant: 'error'
                         })
                     );
                 }
             },error =>{
                 this.showSpinner = false;
                 console.error(error);
                 this.dispatchEvent(
                     new ShowToastEvent({
                         title: 'Error',
                         message: error.body.message,
                         variant: 'error'
                     })
                 );
             },
         );
     }

     //validate deduction invoice
     validateDeductionInvoice(){
         let _validate = true;

         this.template.querySelectorAll('lightning-input').forEach((item) => {
             if (!item.reportValidity()) {
                 _validate = false;
             }
         })
         this.template.querySelectorAll('c-ccm-autocomplete').forEach((item) => {
             if (!item.reportValidity()) {
                 _validate = false;
             }
         })


        let _invoiceAmountTotal = 0;
        this.invoiceItems.forEach(item=>{
            console.log(Number(item.amount));
            _invoiceAmountTotal += item.amount;
        });

         if((!this.isCanada && _invoiceAmountTotal != this.totalClaimAmount) || (this.isCanada && _invoiceAmountTotal != this.summaryTotalAmount)){
             this.dispatchEvent(
                 new ShowToastEvent({
                     title: 'Error',
                     message: "Deduction amounts must be equal to the total claim amount.",
                     variant: 'error'
                 })
             );
             _validate = false;
         }

         return _validate;
     }

     //Preview upload files
     navigateToFiles(event) {
         let _docId = event.currentTarget.dataset.docid;
         this[NavigationMixin.Navigate]({
             type: 'standard__namedPage',
             attributes: {
                 pageName: 'filePreview'
             },
             state : {
                 recordIds: _docId,
                 selectedRecordId: _docId
             }
         })
     }

     handleClickCloseApproveCommentsModel(event) {
         this.showApprovalCommentsModal = false;
     }
     handleClickOpenApproveCommentsModel() {
         this.showApprovalCommentsModal = true;
     }

     handleComments(event) {
         this.approvalComments = event.detail.value;
     }

     handleShipToInputChange(event) {
         let _input = event.detail;
         if (_input.trim() === "") {
             this.selectedShippingAddress = {};
         }
     }

     handleBillToInputChange(event) {
         let _input = event.detail;
         if (_input.trim() === "") {
             this.selectedBillingAddress = {};
         }
     }

     handleProgramInputChange(event) {
         let _input = event.detail;
         if (_input.trim() === "") {
             this.selectedShippingAddress = {};
         }
     }
 }