/**
* @Author: <PERSON>
* @Date: 2017-12-11 15:21:47
* @Description:  
* @Test_Class: WarrantyServiceTest.cls
* @Related_Class: 
---------------------------------------------------
* @Last_Modified_by: <PERSON>
* @Last_Modified_time: 2018-11-26 11:18:57
* @Modifiy_Purpose: 
*/
public with sharing class WarrantyService {
    public Warranty__c warranty;  //当前warranty的
    public List<Warranty_Item__c> warrantyItemList; //最新warranty下的itemlist
    public PricebookEntry masterProPBE; //价格手册
    public List<Warranty_Item__c> warrantyItemOldList; //更新之前的老的olditemlist
    public transient Attachment receiptAttachment; //发票的附件对象
    public Id masterProductOld; //old master product
    public Id receiptAttachmentID; //发票附件记录id
    public Set<String> authorizedPurchasePlaceSet; 
    public Map<String,Integer> productPeriodMap; 
    public Date defaultDate; //默认日期
    public Map<String, String> productTypeMap; //产品类型map

    public WarrantyService() {
        warranty                   = new Warranty__c();
        warrantyItemList           = new List<Warranty_Item__c>();
        warrantyItemOldList        = new List<Warranty_Item__c>();
        masterProPBE               = new PricebookEntry();
        productTypeMap             = new Map<String, String>();
        // authorizedPurchasePlaceSet = new Set<String>{'Home Depot', 'Amazon LLC', 'Dealer'};
        authorizedPurchasePlaceSet = new Set<String>();

        // //获取授权购买地的列表
        // String authorizedPurchasePlaceStr = QueryUtils.getConstantByFunctionName('AuthorizedPurchasePlace');
        // if(authorizedPurchasePlaceStr != '') {
        //  authorizedPurchasePlaceSet.addAll(authorizedPurchasePlaceStr.split(';'));
        // }
        
        // productPeriodMap = new Map<String, Integer>{
        //                      'EGO_R_T'  => 5, 'EGO_R_BC' => 3, 'EGO_R_R'  => 1,
        //                      'EGO_IPC_T'  => 1, 'EGO_IPC_BC' => 1, 'EGO_IPC_R'  => 90,
        //                      'Skil_Old_R_T'  => 5, 'Skil_Old_R_BC' => 1, 'Skil_Old_R_R'  => 1,
        //                      'Skil_Old_IPC_T'  => 1, 'Skil_Old_IPC_BC' => 1, 'Skil_Old_IPC_R'  => 90,
        //                      'EGO_IPC_T'  => 1, 'EGO_IPC_BC' => 1, 'EGO_IPC_R'  => 90,
        //                      'EGO_IPC_T'  => 1, 'EGO_IPC_BC' => 1, 'EGO_IPC_R'  => 90,
        //                      'EGO_IPC_T'  => 1, 'EGO_IPC_BC' => 1, 'EGO_IPC_R'  => 90,
        //                      'EGO_IPC_T'  => 1, 'EGO_IPC_BC' => 1, 'EGO_IPC_R'  => 90,

        //                  };
        defaultDate = Date.newInstance(2013, 3, 1);
    }


    /**
     *
     * @Function: 根据购买地和购买时间，设置 Store return/exchange policy 的值
     *
    */
    public void setStoreReturnExchangePolicy() {
        this.Warranty.Store_return_exchange_policy__c = null;

        if(this.Warranty.Purchase_Date__c != null && 
            String.isNotBlank(this.Warranty.Place_of_Purchase_picklist__c)) {
            //add by mike 20180509 for 增加品牌化
            if(this.Warranty.Brand_Name__c == 'Skil') {
                if(this.Warranty.Place_of_Purchase_picklist__c == 'Home Depot' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Lowes' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Menards' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Meijer' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Wal-mart' ) {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(this.Warranty.Place_of_Purchase_picklist__c == 'Amazon LLC' || 
                            this.Warranty.Place_of_Purchase_picklist__c == 'White Cap' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'Tool Barn' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'Acme Tools' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'Authorized Dealer') {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(-1);
                }
            } else if(this.Warranty.Brand_Name__c == 'SkilSaw') {
                if(this.Warranty.Place_of_Purchase_picklist__c == 'Home Depot' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Lowes' ||
                    this.Warranty.Place_of_Purchase_picklist__c == 'Menards' ) {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(this.Warranty.Place_of_Purchase_picklist__c == 'Ferguson' ) {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(60).addDays(1);
                } else if(this.Warranty.Place_of_Purchase_picklist__c == 'Amazon LLC' || 
                            this.Warranty.Place_of_Purchase_picklist__c == 'Fastenal' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'Grainger' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'White Cap' ||
                            this.Warranty.Place_of_Purchase_picklist__c == 'Authorized Dealer') {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(-1);
                }
            } else {
                if(this.Warranty.Place_of_Purchase_picklist__c == 'Home Depot') {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(90).addDays(1);
                } else if(this.Warranty.Place_of_Purchase_picklist__c == 'Amazon LLC' || 
                            this.Warranty.Place_of_Purchase_picklist__c == 'Authorized Dealer' ||
                            this.warranty.Place_of_Purchase_picklist__c == 'Ace Hardware') {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(30).addDays(1);
                } else {
                    this.Warranty.Store_return_exchange_policy__c = this.Warranty.Purchase_Date__c.addDays(-1);
                }
            }
        }
    }

    /**
     * TODO
     * @Function: 设置warranty item 的过期时间 判断逻辑
     * Modify bullet
     * 添加参数标识是否执行红绿灯计算
     *
    */
    public void setWarrantyItemExpirationDate(Boolean flag) {
        System.debug(this.warranty.Product_Use_Type2__c);
        //找出当前warrantyItem中包含的product对应的warranty year
        Map<String,Decimal> product_warrantyYear_map = new Map<String,Decimal>();
        Set<String> productSet = new Set<String>();
        for(Warranty_Item__c item :this.warrantyItemList){
            productSet.add(item.Product__c);
        }
        for(Product2 pro:[Select Id,Warranty_Year__c From Product2 Where Id in:productSet]){
            Decimal year = pro.Warranty_Year__c==null?99:pro.Warranty_Year__c;
            product_warrantyYear_map.put(pro.Id, year);
        }
        //设置产品明细的过期时间  不同品牌有不同的过期日期处理逻辑
        if(this.warranty.Brand_Name__c == 'Skil' || this.warranty.Brand_Name__c == 'SkilSaw') {
            for(Warranty_Item__c warrantyItem : this.warrantyItemList) {
                setWarrantyItemExpirationDateForSkilAndSkilSaw(warrantyItem,product_warrantyYear_map);
            }
        }else {
            for(Warranty_Item__c warrantyItem : this.warrantyItemList) {
                setWarrantyItemExpirationDateForEGO(warrantyItem);
            }
        }
        //处理完所有过期日期之后  设置指示灯
        if(flag){
            setWarrantyItemIndicator();
        }
    }

    /**
     * [setWarrantyItemExpirationDateForSkilAndSkilSaw description]
     * <AUTHOR>
     * @Date        2018-08-22
     * @Description Add          Description
     * @param       warrantyItem [description]
     */
    public void setWarrantyItemExpirationDateForSkilAndSkilSaw(Warranty_Item__c warrantyItem,Map<String,Decimal> product_warrantyYear_map){
        if(this.warranty.Purchase_Date__c==null){
            return;
        }
        //商用  skil->90天  skil->1 year
        //民用  根据warranty year
        if(this.warranty.Product_Use_Type2__c == 'Residential' && product_warrantyYear_map.containsKey(warrantyItem.Product__c)){
            Integer year =Integer.valueOf(product_warrantyYear_map.get(warrantyItem.Product__c) );
            warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(year);
        }else if(this.warranty.Product_Use_Type2__c == 'Professional/Commercial'){
            //商用的
            if(this.warranty.Brand_Name__c == 'Skil'){
                warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addDays(90);
            }else if(this.warranty.Brand_Name__c == 'SkilSaw'){
                warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(1);
            }
        }
    }

    public void setWarrantyItemExpirationDateForEGO(Warranty_Item__c warrantyItem) {
        //ADD BULLET
        if(this.warranty.Purchase_Date__c==null){
            return;
        }
        if(this.warranty.Product_Use_Type2__c == 'Residential') {
            if(warrantyItem.Product_Model__c == '3') {
                warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(1);
            } else {
                if(warrantyItem.Product_Type__c == 'Product') {
                    warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(5);
                } else {
                    warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(3);
                }
            }
        }else if(this.warranty.Product_Use_Type2__c == 'Professional/Commercial'){
            //如果是翻新机，这过保日期为90天
            if(warrantyItem.Product_Model__c == '3') {
                warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addDays(90);
            } else {
                warrantyItem.Expiration_Date__c = this.warranty.Purchase_Date__c.addYears(1);
            }
        }
    }

    /**
     *
     * @Function: 设置warranty item 的指示灯
     *
    */
    public void setWarrantyItemIndicator() {
        //判断购买地是否授权
        String isAuthorized = getPurchasePlaceIsAuthorized();
        System.debug('isAuthorized='+isAuthorized);
        //设置warranty明细的指示灯
        for(Warranty_Item__c warrantyItem : this.warrantyItemList) {
            //购买地未授权
            if(isAuthorized == 'Unauthorized') {
                System.debug('isAuthorized11111111');
                warrantyItem.Indicator__c = 'Out of Warranty';//购买地状态为授权
                continue;
            }

            //过期
            if(warrantyItem.Expiration_Date__c != null &&
                warrantyItem.Expiration_Date__c.daysBetween(Date.today()) > 0) {
                System.debug('isAuthorized2222222');
                warrantyItem.Indicator__c = 'Out of Warranty';//过期
            } else {
                if(isAuthorized == 'Unknown') {
                    System.debug('isAuthorized333333');
                    warrantyItem.Indicator__c = 'Pending';//未过期，但购买地状态为Unknown
                } else {
                    if(this.warranty.Receipt_received_and_verified__c) {
                        if(!String.isBlank(warrantyItem.Serial_Number__c)) {
                            System.debug('isAuthorized44444');
                            warrantyItem.Indicator__c = 'Vailid Warranty';//购买地状态为Authorized，未过期，发票上传且验证过
                        } else {
                            System.debug('isAuthorized777777');
                            warrantyItem.Indicator__c = 'Pending';
                        }
                    } else {
                        if(this.warranty.Lost_Receipt__c) {
                            System.debug('isAuthorized5555');
                            if(this.warranty.Order_Times__c == 0) {
                                warrantyItem.Indicator__c = 'Lost Receipt';//购买地状态为Authorized，未过期，未上传或验证发票，发票丢失了
                            } else {
                                warrantyItem.Indicator__c = 'Out of Warranty';
                            }
                        } else {
                            System.debug('isAuthorized66666');
                            warrantyItem.Indicator__c = 'Pending';//购买地状态为Authorized，未过期，未上传或验证发票，发票未丢失
                        }
                    }
                }
            }
        }
    }

    /**
     *
     * @Function: 判断购买地是否授权
     *
    */
    public String getPurchasePlaceIsAuthorized() {
        //modify by mike 20180509 for 将授权的购买地通过配置形式保存
        if(this.warranty.Place_of_Purchase_picklist__c.containsIgnoreCase('Unauthorized')) {
            return 'Unauthorized';
        } else if(this.warranty.Place_of_Purchase_picklist__c.containsIgnoreCase('Unknown') || 
                    this.warranty.Place_of_Purchase_picklist__c.containsIgnoreCase('Other')) {
            return 'Unknown';
        } else {
            return 'Authorized';
        }
    }

    /**
     *
     * @Function: 计算修理/退货费用
     *
    */
    public void countCost() {
        //如果购买地未知，按照Other进行操作
        //如果购买地未知，或者是已授权的dealer，按other操作
        String purchasePlace = '';
        if(this.warranty.Place_of_Purchase_picklist__c == 'Home Depot' || 
                this.warranty.Place_of_Purchase_picklist__c == 'Amazon LLC' ||
                this.warranty.Place_of_Purchase_picklist__c == 'Unauthorized Dealer' ||
                this.warranty.Place_of_Purchase_picklist__c == 'Ace Hardware') {
            purchasePlace = this.warranty.Place_of_Purchase_picklist__c;
        } else {
            purchasePlace = 'Other';
        }

        if(purchasePlace == 'Unauthorized Dealer') {
            this.warranty.Recommendation_of_Warranty_Issue__c = 'Return to place of purchase.';
            return;
        } 

        //如果找不到相应的价格手册信息，报错
        Default_PriceBook__c priceBook = ProductService.getPriceBookByPurchasePlace(purchasePlace);
        if(priceBook == null) {
            ApexPages.Message myMsg = new ApexPages.Message(ApexPages.Severity.WARNING, 'CAN NOT FIND THE DATA OF MAINTENANCE COST!');
            ApexPages.addMessage(myMsg);
            return;
        }

        //获取价格手册
        masterProPBE = ProductService.getPriceBookEntryByProductAndPrice(priceBook.Price_Book_Id__c, this.warranty.Master_Product__c);
        
        //根据购买地计算费用
        if(purchasePlace == 'Home Depot') { //THD
            //THD维修费用
            this.warranty.Store_Policy_Cost__c = (masterProPBE.UnitPrice==null?0:masterProPBE.UnitPrice)*1.1;                                                                        
            Decimal shippingFee = 10;                                                                                                                                                                                                                                                                                                
            this.warranty.Replacement_Cost__c = (masterProPBE.Product2.Landed_Cost__c==null?0:masterProPBE.Product2.Landed_Cost__c + 
                                                    masterProPBE.Product2.Pick_up_Fee__c==null?0:masterProPBE.Product2.Pick_up_Fee__c + 
                                                        shippingFee);

            //计算EGO warranty period
            Integer warrantyPeriod = Integer.valueOf(masterProPBE.Product2.THD_store_warranty_period__c);                                                                                    
            warrantyPeriod = warrantyPeriod==null ? 0 : warrantyPeriod;                                                                                                                                                                                          
            Date EGO_warranty_period = this.warranty.Purchase_Date__c.addDays(warrantyPeriod).addDays(1);                                                                                    

            //修改建议
            if(this.warranty.Purchase_Date__c.addDays(90) >= Date.today() && this.warranty.Replacement_Cost__c >= this.warranty.Store_Policy_Cost__c) {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Exchange the product via store.';
            } else if(this.warranty.Purchase_Date__c.addDays(90) >= Date.today() && this.warranty.Replacement_Cost__c < this.warranty.Store_Policy_Cost__c) {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Send replacement to customer.';
            } else if(this.warranty.Purchase_Date__c.addDays(90) < Date.today() && Date.today() <= EGO_warranty_period) {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Repair via authorized repair center freely.';
            } else {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.';
            }
        } else if(purchasePlace == 'Amazon LLC') { //Amazon
            //Amazon维修费用
            this.warranty.Store_Policy_Cost__c = (masterProPBE.UnitPrice==null?0:masterProPBE.UnitPrice);
            Decimal shippingFee = 10;
            this.warranty.Replacement_Cost__c = (masterProPBE.Product2.Landed_Cost__c==null?0:masterProPBE.Product2.Landed_Cost__c + 
                                                    masterProPBE.Product2.Pick_up_Fee__c==null?0:masterProPBE.Product2.Pick_up_Fee__c + 
                                                        shippingFee);

            //计算EGO warranty period
            Integer warrantyPeriod = Integer.valueOf(masterProPBE.Product2.Amazon_Store_warranty_period__c);
            warrantyPeriod = warrantyPeriod==null ? 0 : warrantyPeriod;
            Date EGO_warranty_period = this.warranty.Purchase_Date__c.addDays(warrantyPeriod).addDays(1);
            //修改建议
            if(this.warranty.Purchase_Date__c.addDays(30) >= Date.today() && this.warranty.Replacement_Cost__c >= this.warranty.Store_Policy_Cost__c)
            {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Exchange the product via store.';
            }else if(this.warranty.Purchase_Date__c.addDays(30) >= Date.today() && this.warranty.Replacement_Cost__c < this.warranty.Store_Policy_Cost__c)
            {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Send replacement to customer.';
            }else if(this.warranty.Purchase_Date__c.addDays(30) < Date.today() && Date.today() <= EGO_warranty_period)
            {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Repair via authorized repair center freely.';
            }else
            {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.';
            }
        } else if(purchasePlace == 'Other') {
            //
            Product2 thisProduct = ProductService.getProduct2ByID(getWarrantyItemProductId());
            System.debug(thisProduct);
            //Amazon维修费用
            this.warranty.Store_Policy_Cost__c = null;
            this.warranty.Replacement_Cost__c = null;

            //计算EGO warranty period
            Integer warrantyPeriod;
            if(thisProduct != null) {
                warrantyPeriod = this.warranty.Product_Use_Type2__c=='Professional/Commercial'?1:Integer.valueOf(thisProduct.Dealer_Grainger_Store_warranty_period__c);
            } else {
                warrantyPeriod = 0;
            }
            // Integer warrantyPeriod = this.warranty.Product_Use_Type2__c=='Professional/Commercial'?1:Integer.valueOf(thisProduct.Dealer_Grainger_Store_warranty_period__c);
            warrantyPeriod = warrantyPeriod==null ? 0 : warrantyPeriod;
            Date EGO_warranty_period = this.warranty.Purchase_Date__c.addDays(warrantyPeriod).addDays(1);
            
            //修改建议
            if(this.warranty.Purchase_Date__c.addDays(30) >= Date.today()) {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Get exchange through dealer.';
            } else if(this.warranty.Purchase_Date__c.addDays(30) < Date.today() && 
                        Date.today() <= EGO_warranty_period) {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Direct the customer to the authorized service center for free repair.';
            } else {
                this.warranty.Recommendation_of_Warranty_Issue__c = 'Repair via authorized repair center, recovering fee will be charged; Or purchase a new one.';
            }
        }
    }

    /**
     *
     * @Function: 获取warranty item中type为product的产品的Id
     *
    */
    public String getWarrantyItemProductId() {
        for(Warranty_Item__c warrantyItem : warrantyItemList){
            return warrantyItem.Product__c;
            // if(warrantyItem.Product_Type__c == 'Product'){
            //  return warrantyItem.Product__c;
            // }
        }
        return '';
    }

    /**
     *
     * @Function: 若没有修改master product，更新warrant item,
     *          若修改或新增master product，则插入新的warranty item，并删除旧的
     *
    */
    public void saveWarrantyItem() {
        if(warrantyItemList.size() <= 0){
            return ;
        }
        System.debug('this.warranty.Master_Product__c='+this.warranty.Master_Product__c);
        System.debug('masterProductOld='+masterProductOld);
        //若master product发生变化，则删除旧的warranty item，插入新的item
        if(this.warranty.Master_Product__c != masterProductOld) {
            System.debug('masterproduct change222');
            //将warranty item与warranty建立关系
            for(Warranty_Item__c warrantyItem : warrantyItemList){
                if (warrantyItem.Warranty__c == null) {
                    warrantyItem.Warranty__c = this.warranty.Id;
                }
            }
            //插入新的
            if(warrantyItemList.size() > 0) {
                insert warrantyItemList;
            }
            //删除旧的
            if(warrantyItemOldList.size() > 0){
                delete warrantyItemOldList;
            }
        } else {
            //若master product没变，则更新warranty item
            if(warrantyItemList.size() > 0) {
                update warrantyItemList;
            }
        } 
    }

    /**
     *
     * @Function: 上传发票附件
     * @Param: [imageBody, Blob, 附件内容]
     * @Param: [contentType, Blob, 附件类型]
     *
    */
    public Boolean uploadReceiptAttachment(Blob imageBody, String contentType) {
        receiptAttachment = new Attachment();
        receiptAttachment.Body                 = imageBody;
        receiptAttachment.Name                 = 'Recepit Image Temp';
        receiptAttachment.ContentType          = contentType;
        receiptAttachment.ParentId             = this.warranty.AccountCustomer__c == null ? 
                                                    [Select Id from Account limit 1].Id : 
                                                    this.warranty.AccountCustomer__c;
        try {
            insert receiptAttachment;
            receiptAttachmentID = receiptAttachment.Id;
            this.warranty.Image_of_Receipt__c = QueryUtils.getSalesforceUrl() + 
                                                '/servlet/servlet.FileDownload?file=' + 
                                                receiptAttachment.Id;
            return true;
        } catch(Exception ex) {
            ApexPages.Message myMsg = new ApexPages.Message(ApexPages.Severity.ERROR, 'Upload Failed: ' + ex.getMessage());
            ApexPages.addMessage(myMsg);
            System.debug(ex.getMessage());
            return false;
        }
    }

    /**
     *
     * @Function: 建立发票和warranty的关系
     *
    */
    public void relateWarrantyAndAttachment() {
        if(this.receiptAttachmentID != null){
            transient Attachment atta = [SELECT Id,Name,Body,ContentType,ParentId FROM Attachment 
                                        WHERE Id =: this.receiptAttachmentID LIMIT 1];
            receiptAttachment = new Attachment();
            receiptAttachment.Body = atta.Body;
            receiptAttachment.ContentType = atta.ContentType;
            receiptAttachment.Name     = 'Recepit Image';
            receiptAttachment.ParentId = this.warranty.Id;
            insert receiptAttachment;
            delete atta;
        }
    }

    /**
     *
     * @Function: 校验sn格式是否符合要求
     * @Param: [serialNumber, String, 需要校验的sn]
     *
    */
    public static Boolean verifySerialNumber(String serialNumber) {
        serialNumber = serialNumber.toUpperCase();
        List<String> snRegExpList = QueryUtils.getSerialNumberRegExp();
        if(snRegExpList == null || snRegExpList.size() == 0){
            return true;
        }
        //匹配正则表达式
        Pattern p;
        Matcher m;
        for(String snRegExp : snRegExpList){
            p = Pattern.compile(snRegExp);
            m = p.matcher(serialNumber);
            if(m.matches()){
                return true;
            }
        }
        return false;
    }
    /**
     *@Function: 校验sn格式是否符合要求
     * @Param: [BrandName,serialNumber, String, 需要校验的sn] 
     */
    public static Boolean verifySerialNumber(String brandName,String serialNumber,String modal){
        serialNumber = serialNumber.toUpperCase();
        System.debug('=====>'+brandName+'====>'+serialNumber);
        //获得系统中所有有效的验证规则  保证正则有  Name有
        Map<String,System_Configuration__c> snRegExpMap = QueryUtils.getSerialNumberRegExpMap();
        if(snRegExpMap.keySet().size()==0){
            return true;
        }
        //根据品牌不同  SN校验不同  校验EGO Hamerhead
        List<System_Configuration__c> scList = snRegExpMap.values();
        //判断品牌为 null ''
        if(String.isBlank(brandName)){
            return false;
        }
        
        //匹配正则表达式
        Pattern p;
        Matcher m;
        for(System_Configuration__c sc : scList){
            String regStr = null;
            //根据不同的品牌，查询对应的SN规则
            if(!String.isBlank(sc.Name) && sc.Name.containsIgnoreCase(brandName + '_')) {
                regStr = sc.RegExp__c;
            }

            if(!String.isBlank(regStr)){
                p = Pattern.compile(regStr);
                m = p.matcher(serialNumber);
                if(m.matches()){
                    return true;
                }
            }
        }

        // 允许特定产品并且在一定SN范围内的通过校验
        if(modal =='SC535801' && serialNumber.length() <= 9){
            Integer intSN = Integer.valueof(serialNumber);
            System.debug('222222222  '+ intSN);
            if(intSN >= Integer.valueof('420000001') && intSN <= Integer.valueof('420015000')){
                System.debug('420开头的SN');
                return true;
            }
        }
        return false;
    }


    public static Boolean verifySerialNumber(String brandName, String serialNumber, String modal, Boolean isEdit){
        Boolean isQualified = verifySerialNumber(brandName, serialNumber, modal);

        if(!isEdit && modal.endsWithIgnoreCase('-FC')) {
            Map<String,System_Configuration__c> snRegExpMap = QueryUtils.getSerialNumberRegExpMap();
            List<System_Configuration__c> scList = new List<System_Configuration__c>();
            scList.add(snRegExpMap.get('FC_v2'));
            scList.add(snRegExpMap.get('EGO_v4'));

            Pattern p;
            Matcher m;
            for(System_Configuration__c sc : scList){
                String regStr = sc.RegExp__c;
                if(!String.isBlank(regStr)){
                    p = Pattern.compile(regStr);
                    m = p.matcher(serialNumber);
                    if(m.matches()){
                        return true;
                    }
                }
            }
            return false;
        }
        return isQualified;
    }


    /*************************Query Util*****************************************/

    /**
     *
     * @Function: 通过Id获取warranty
     * @param [id, String,  warranty的Id]
     *
    */
    public static Warranty__c getWarrantyByID(String id){
        for(Warranty__c warranty : [SELECT Id,Store_return_exchange_policy__c, Name,Place_of_Purchase_lu__c, Master_Product__r.ProductCode, 
                                        Product_Serial_Number__c, Product__c, Purchase_Date__c, Battery_Serial_Number__c, Brand_Name__c,
                                        Charger_Serial_Number__c, Resend_Registration_Confirmation__c, Battery__c, Charger__c, Invalid_Receipt__c,
                                        Place_of_Purchase__c, Gift__c, Receipt_received_and_verified__c, Within_Store_Policy__c, Pending__c,
                                        Expiration_Date__c, Recommendation_of_Warranty_Issue__c, Image_of_Receipt__c, Master_Product__c, Production_Date__c,
                                        Product_Use_Type2__c, Store_Policy_Cost__c, Replacement_Cost__c, Lost_Receipt__c, Receipt_upload__c,
                                        Place_of_Purchase_picklist__c, Battery2__c, Battery2_Serial_Number__c, Product_Expiration_Date__c, 
                                        Battery_Expiration_Date__c, Battery2_Expiration_Date__c, Charge_Expiration_Date__c, AccountCustomer__c,
                                        Order_Times__c, Within_Store_Policy_Formula__c, AccountCustomer__r.Record_Type_Name__c,CreatedDate,One_Time_Exception__c,
                                        (SELECT Id,Expiration_Date__c,Historical_SN__c,Product__c,Product_Code__c,Product_Type__c,Product_Name__c,
                                            Serial_Number__c,Warranty__c,Product__r.Type__c,Product__r.ProductCode,Product__r.Parent_Product__c,
                                            ActualIndicator__c,IsReplacement__c,Indicator__c,Product__r.Warranty_Year__c,Expiration_Date_Formula__c,
                                            Product__r.ProductModel__c,Product_Model__c, Product__r.Name, Product__r.Product_Type__c,Expiration_Date_new__c
                                         FROM Warranty_Items__r ORDER BY Product__r.Type__c) 
                                    FROM Warranty__c WHERE Id =: id]) {
            return warranty;
        }
        return null;
    }

    /**
     *
     * @Function: 通过Id获取warranty（简单属性）
     * @param [id, String,  warranty的Id]
     *
    */
    public static Warranty__c getWarrantySimpleByID(String id){
        // Warranty__c warranty = new Warranty__c();
        for(Warranty__c warranty : [SELECT Id,Master_Product__r.ProductModel__c,
                                        Master_Product__c,Order_Times__c,Brand_Name__c,//Added by John Jiang on ********
                                        AccountCustomer__c, Name, //Added by chengshuai on ******** for SF-Service-004-001-001
                                        (SELECT Id,Product__c,Product__r.ProductCode,Product__r.Brand_Name__c,
                                            ActualIndicator__c   
                                        FROM Warranty_Items__r 
                                        WHERE ActualIndicator__c = 'Vailid Warranty'
                                        ORDER BY Product__r.Type__c) 
                                    FROM Warranty__c WHERE Id =: id]) {
            return warranty;
        }
        return null;
    }

    public static Warranty__c getWarrantySimpleByIDforCase(String id){
        // Warranty__c warranty = new Warranty__c();
        for(Warranty__c warranty : [SELECT Id,Master_Product__r.ProductModel__c,
                                        Master_Product__c,Order_Times__c,Brand_Name__c,//Added by John Jiang on ********
                                        AccountCustomer__c, Name, //Added by chengshuai on ******** for SF-Service-004-001-001
                                        (SELECT Id,Product__c,Product__r.ProductCode,Product__r.Brand_Name__c,
                                            ActualIndicator__c   
                                        FROM Warranty_Items__r 
                                        ORDER BY Product__r.Type__c) 
                                    FROM Warranty__c WHERE Id =: id]) {
            return warranty;
        }
        return null;
    }

    /**
     *
     * @Function: 通过Id获取warranty（简单属性）
     * @param [id, String,  warranty的Id]
     *
    */
    public static Map<String, Warranty__c> getWarrantyMapByIdSet(Set<String> warrantyIdSet){
        Map<String, Warranty__c> warrantyIdAndWarrantyMap  = new Map<String,Warranty__c>();
        for(Warranty__c warranty : [SELECT Id, CustomerEmail__c, AccountCustomer__r.Name,Brand_Name__c,
                                        (SELECT Id,Expiration_Date__c,Historical_SN__c,Product__c,
                                            Product_Code__c,Product_Type__c,Product_Name__c,
                                            Serial_Number__c,Warranty__c,Product__r.Type__c,
                                            Product__r.ProductCode,Indicator__c,IsReplacement__c,
                                            ActualIndicator__c,Customer_Email__c    
                                        FROM Warranty_Items__r) 
                                    FROM Warranty__c WHERE Id in: warrantyIdSet]) {
            warrantyIdAndWarrantyMap.put(Warranty.Id, warranty);
        }
        return warrantyIdAndWarrantyMap;
    }

    /**
     *
     * @Function: 通过Id获取warranty（简单属性）
     * @param [id, String,  warranty的Id]
     *
    */
    public static Map<String, Warranty__c> getWarrantyNoItemsMapByIdSet(Set<String> warrantyIdSet){
        Map<String, Warranty__c> warrantyIdAndWarrantyMap  = new Map<String,Warranty__c>();
        for(Warranty__c warranty : [SELECT Id, CustomerEmail__c, AccountCustomer__r.Name,
                                            Product_Use_Type2__c,Purchase_Date__c
                                    FROM Warranty__c WHERE Id in: warrantyIdSet]) {
            warrantyIdAndWarrantyMap.put(Warranty.Id, warranty);
        }
        return warrantyIdAndWarrantyMap;
    }

    /**
     *
     * @Function: 通过Id获取warranty（包括相关的order）
     * @param [warrantyId, String,  warranty的Id]
     *
    */
    public static Warranty__c getWarrantyAndOrderById(String warrantyId){
        for(Warranty__c warranty : [SELECT Id, Receipt_received_and_verified__c, Lost_Receipt__c, 
                                        Within_Store_Policy__c, Place_of_Purchase_picklist__c, One_Time_Exception__c,
                                        (SELECT Id FROM Order__r), (SELECT Product_Code__c,ActualIndicator__c FROM Warranty_Items__r) 
                                    FROM Warranty__c WHERE Id =: warrantyId]) {
            return warranty;
        }
        return null;
    }

    /**
     *
     * @Function: 通过Id set获取warrantyList（包括相关的order）
     * @param [warrantyIdSet, set,  warranty的Id]
     *
    */
    public static List<Warranty__c> getWarrantyAndOrderByIdSet(Set<String> warrantyIdSet){
        return [SELECT Id,Order_Times__c,(SELECT Id FROM Order__r) 
                FROM Warranty__c WHERE Id IN: warrantyIdSet];
    }

    /**
     *
     * @Function: 通过warranty id获取warranty item
     * @param [warrantyId, String,  warranty的Id]
     *
    */
    public static List<Warranty_Item__c> getWarrantyItemByWarrantyId(String warrantyId){
        return [SELECT Id,Name,Product__r.Name,Product__r.ProductCode 
                FROM Warranty_Item__c WHERE Warranty__c =: warrantyId];
    }


    /**
     * @Function Get the Warranty__c throught warrantyName
    */
    public static Warranty__c getWarrantyByWarrantyIds(String warrantyId){
        if(warrantyId == null){
            return new Warranty__c();
        }
        return [SELECT Id, Within_Store_Policy__c, Store_return_exchange_policy__c,Within_Store_Policy_Formula__c, 
                    Store_Policy_Cost__c, Replacement_Cost__c, Recommendation_of_Warranty_Issue__c 
                    FROM Warranty__c 
                    WHERE Id =:warrantyId];
    }
    public static Warranty__c getWarrantyByWarrantyName(String warrantyName){
        Warranty__c warranty = new Warranty__c();
        for(Warranty__c obj : [Select id,Within_Store_Policy__c,Within_Store_Policy_Formula__c,
                                        Store_return_exchange_policy__c,
                                        Store_Policy_Cost__c,
                                        Replacement_Cost__c,
                                        Recommendation_of_Warranty_Issue__c
                                        From Warranty__c 
                                        Where Name=:warrantyName
            ]){
            warranty = obj;
        }
        return warranty;
    }
}