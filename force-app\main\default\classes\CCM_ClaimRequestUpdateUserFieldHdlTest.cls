/**
 * @Author:     AI Assistant
 * @Date:       2024-12-19
 * @Return:     
 * @Function:   CCM_ClaimRequestUpdateUserFieldHandler的测试类
 * @Last_Modified_by:  
 * @Last_Modified_time:
 * @Modifiy_Purpose:   提升测试覆盖率到90%
 */
@IsTest
public class CCM_ClaimRequestUpdateUserFieldHdlTest {
    
    @TestSetup
    static void setupTestData() {
        // 创建用户角色
        UserRole salesRole = new UserRole();
        salesRole.Name = 'Test Sales Manager';
        salesRole.DeveloperName = 'Test_Sales_Manager';
        insert salesRole;
        
        UserRole directorRole = new UserRole();
        directorRole.Name = 'Test Sales Director';
        directorRole.DeveloperName = 'Test_Sales_Director';
        directorRole.ParentRoleId = salesRole.Id;
        insert directorRole;
        
        UserRole vpRole = new UserRole();
        vpRole.Name = 'Test Sales VP';
        vpRole.DeveloperName = 'Test_Sales_VP';
        insert vpRole;
        
        // 创建用户
        Profile p = [SELECT Id FROM Profile WHERE Name='Standard User' LIMIT 1];
        
        User salesManager = new User();
        salesManager.Alias = 'sales';
        salesManager.Email = '<EMAIL>';
        salesManager.EmailEncodingKey = 'UTF-8';
        salesManager.LastName = 'Sales Manager';
        salesManager.LanguageLocaleKey = 'en_US';
        salesManager.LocaleSidKey = 'en_US';
        salesManager.ProfileId = p.Id;
        salesManager.TimeZoneSidKey = 'America/Los_Angeles';
        salesManager.UserName = 'salesmanager' + System.currentTimeMillis() + '@test.com';
        salesManager.UserRoleId = salesRole.Id;
        insert salesManager;
        
        User salesDirector = new User();
        salesDirector.Alias = 'director';
        salesDirector.Email = '<EMAIL>';
        salesDirector.EmailEncodingKey = 'UTF-8';
        salesDirector.LastName = 'Sales Director';
        salesDirector.LanguageLocaleKey = 'en_US';
        salesDirector.LocaleSidKey = 'en_US';
        salesDirector.ProfileId = p.Id;
        salesDirector.TimeZoneSidKey = 'America/Los_Angeles';
        salesDirector.UserName = 'salesdirector' + System.currentTimeMillis() + '@test.com';
        salesDirector.UserRoleId = directorRole.Id;
        insert salesDirector;
        
        User salesVP = new User();
        salesVP.Alias = 'vp';
        salesVP.Email = '<EMAIL>';
        salesVP.EmailEncodingKey = 'UTF-8';
        salesVP.LastName = 'Sales VP';
        salesVP.LanguageLocaleKey = 'en_US';
        salesVP.LocaleSidKey = 'en_US';
        salesVP.ProfileId = p.Id;
        salesVP.TimeZoneSidKey = 'America/Los_Angeles';
        salesVP.UserName = 'salesvp' + System.currentTimeMillis() + '@test.com';
        salesVP.UserRoleId = vpRole.Id;
        insert salesVP;
        
        // 创建Account
        Account testAccount = new Account();
        testAccount.Name = '测试客户';
        testAccount.TaxID__c = 'TEST001';
        testAccount.AccountNumber = 'ACC001';
        testAccount.OwnerId = salesManager.Id;
        insert testAccount;
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestUpdateUserFieldHandler的用户字段更新 - Sales Manager场景
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testSalesManagerFieldUpdate() {
        Account testAccount = [SELECT Id, OwnerId FROM Account LIMIT 1];
        
        Test.startTest();
        // 创建Claim Request
        Claim_Request__c claimRequest = new Claim_Request__c();
        claimRequest.Customer__c = testAccount.Id;
        claimRequest.Claim_Status__c = 'Draft';
        // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
        
        insert claimRequest;
        
        // 验证Sales Manager字段已设置
        Claim_Request__c insertedRequest = [SELECT Id, Sales_Manager__c, Sales_Director__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals(testAccount.OwnerId, insertedRequest.Sales_Manager__c, 'Sales Manager应该设置为Account Owner');
        System.assertNotEquals(null, insertedRequest.Sales_Director__c, 'Sales Director应该已设置');
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestUpdateUserFieldHandler的Sales Director场景
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testSalesDirectorFieldUpdate() {
        User salesDirector = [SELECT Id FROM User WHERE LastName = 'Sales Director' LIMIT 1];
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        testAccount.OwnerId = salesDirector.Id;
        update testAccount;
        
        Test.startTest();
        // 创建Claim Request
        Claim_Request__c claimRequest = new Claim_Request__c();
        claimRequest.Customer__c = testAccount.Id;
        claimRequest.Claim_Status__c = 'Draft';
        // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
        
        insert claimRequest;
        
        // 验证Sales Director字段已设置
        Claim_Request__c insertedRequest = [SELECT Id, Sales_Manager__c, Sales_Director__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals(salesDirector.Id, insertedRequest.Sales_Manager__c, 'Sales Manager应该设置为Sales Director');
        System.assertNotEquals(null, insertedRequest.Sales_Director__c, 'Sales Director应该已设置');
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestUpdateUserFieldHandler的批量处理
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testBulkProcessing() {
        Account testAccount = [SELECT Id, OwnerId FROM Account LIMIT 1];
        
        Test.startTest();
        // 创建多个Claim Request
        List<Claim_Request__c> claimRequests = new List<Claim_Request__c>();
        for (Integer i = 0; i < 5; i++) {
            Claim_Request__c claimRequest = new Claim_Request__c();
            claimRequest.Customer__c = testAccount.Id;
            claimRequest.Claim_Status__c = 'Draft';
            // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
            claimRequests.add(claimRequest);
        }
        
        insert claimRequests;
        
        // 验证所有记录的Sales Manager字段都已设置
        List<Claim_Request__c> insertedRequests = [SELECT Id, Sales_Manager__c, Sales_Director__c FROM Claim_Request__c WHERE Id IN :claimRequests];
        for (Claim_Request__c cr : insertedRequests) {
            System.assertEquals(testAccount.OwnerId, cr.Sales_Manager__c, '所有记录的Sales Manager应该都已设置');
            System.assertNotEquals(null, cr.Sales_Director__c, '所有记录的Sales Director应该都已设置');
        }
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestUpdateUserFieldHandler的更新场景
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testUpdateScenario() {
        Account testAccount = [SELECT Id, OwnerId FROM Account LIMIT 1];
        
        // 创建Claim Request
        Claim_Request__c claimRequest = new Claim_Request__c();
        claimRequest.Customer__c = testAccount.Id;
        claimRequest.Claim_Status__c = 'Draft';
        // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
        insert claimRequest;
        
        Test.startTest();
        // 更新Claim Request
        claimRequest.Claim_Status__c = 'Pending Review';
        update claimRequest;
        
        // 验证字段仍然正确
        Claim_Request__c updatedRequest = [SELECT Id, Sales_Manager__c, Sales_Director__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals(testAccount.OwnerId, updatedRequest.Sales_Manager__c, 'Sales Manager应该保持正确');
        System.assertNotEquals(null, updatedRequest.Sales_Director__c, 'Sales Director应该保持正确');
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestUpdateUserFieldHandler的空Customer场景
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testNullCustomerScenario() {
        Test.startTest();
        // 创建没有Customer的Claim Request
        Claim_Request__c claimRequest = new Claim_Request__c();
        claimRequest.Customer__c = null;
        claimRequest.Claim_Status__c = 'Draft';
        // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
        
        insert claimRequest;
        
        // 验证字段没有设置
        Claim_Request__c insertedRequest = [SELECT Id, Sales_Manager__c, Sales_Director__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals(null, insertedRequest.Sales_Manager__c, 'Sales Manager应该为空');
        System.assertEquals(null, insertedRequest.Sales_Director__c, 'Sales Director应该为空');
        Test.stopTest();
    }
}
