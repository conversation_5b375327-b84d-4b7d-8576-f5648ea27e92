public without sharing class CCM_ClaimRequestUpdateUserFieldHandler implements Triggers.Handler{
    public void handle(){
        //populate sales manager and sales director
        List<Claim_Request__c> claimList = (List<Claim_Request__c>)Trigger.new;
        Set<Id> customerSet = new Set<Id>();
        for(Claim_Request__c cr : claimList){
            if( cr.Customer__c!=null ){
                customerSet.add(cr.Customer__c);
            }
        }
        //get all customer info and parent user role
        Map<Id,Account> customerMap = new Map<Id,Account>([Select Id,
                                                                OwnerId,
                                                                Owner.UserRoleId,
                                                                Owner.UserRole.Name,
                                                                Owner.UserRole.ParentRoleId 
                                                                From Account 
                                                                where id in:customerSet]);
        Set<Id> parentRoleIdSet = new Set<Id>();
        for(String accId : customerMap.keySet()){
            parentRoleIdSet.add( customerMap.get(accId).Owner.UserRole.ParentRoleId );
        }
        List<User> directorUser = [Select id, UserRoleId, Email From User where UserRoleId in :parentRoleIdSet];
        Map<Id,User> directRoleUserMap = new Map<Id,User>();
        for( User userObj : directorUser ){
            directRoleUserMap.put(userObj.UserRoleId, userObj);
        }
        List<User> salesVPUser = [SELECT Id FROM User WHERE UserRole.Name LIKE '%Sales VP%' AND IsActive = true];

        // for(Claim_Request__c cr : claimList){
        //     if( cr.Customer__c!=null && customerMap.containsKey(cr.Customer__c) ){
        //         if( customerMap.get(cr.Customer__c).Owner.UserRoleId!=null){
        //             if(customerMap.get(cr.Customer__c).Owner.UserRole.Name.contains('Sales Director') ){
        //                 cr.Sales_Manager__c = customerMap.get(cr.Customer__c).OwnerId;
        //                 cr.Sales_Director__c = customerMap.get(cr.Customer__c).OwnerId;
        //             }else{
        //                 cr.Sales_Manager__c = customerMap.get(cr.Customer__c).OwnerId;
        //                 if( directRoleUserMap.containsKey(customerMap.get(cr.Customer__c).Owner.UserRole.ParentRoleId) ){
        //                     cr.Sales_Director__c = directRoleUserMap.get(customerMap.get(cr.Customer__c).Owner.UserRole.ParentRoleId).Id;
        //                 }
        //             }
        //         }
        //     }
        // }
        for (Claim_Request__c cr : claimList) {
            if( cr.Customer__c!=null && customerMap.containsKey(cr.Customer__c) ){
                cr.Sales_Manager__c = customerMap.get(cr.Customer__c).OwnerId;
                if( customerMap.get(cr.Customer__c).Owner.UserRoleId!=null){
                    // if users role is sales director or ka sales manager, set sales vp to sales_director__c
                    if(customerMap.get(cr.Customer__c).Owner.UserRole.Name.contains('Sales Director') || customerMap.get(cr.Customer__c).Owner.UserRole.Name.contains('KA Sales Manager')){
                        // cr.Sales_Director__c = customerMap.get(cr.Customer__c).OwnerId;
                        if (salesVPUser.size() > 0) {
                            cr.Sales_Director__c = salesVPUser[0].Id;
                        } else {
                            cr.Sales_Director__c = customerMap.get(cr.Customer__c).OwnerId;
                        }
                    }else{
                        if( directRoleUserMap.containsKey(customerMap.get(cr.Customer__c).Owner.UserRole.ParentRoleId) ){
                            cr.Sales_Director__c = directRoleUserMap.get(customerMap.get(cr.Customer__c).Owner.UserRole.ParentRoleId).Id;
                        }
                    }
                }
            }
        }
    }
}