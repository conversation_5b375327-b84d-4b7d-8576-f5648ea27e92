@IsTest
private class CCM_WarrantyControllerTest {
    @TestSetup
    static void testDataSet() {
        Test.startTest();
        List<System_Configuration__c> lstSystemConfiguration = new List<System_Configuration__c>{
            new System_Configuration__c(Name = 'Claim Auto Approval', Is_Active__c = true),
            new System_Configuration__c(
                Name = 'Hammerhead_v3',
                RegExp__c = '^[0-9](([13579][0-2])|([24680][1-9]))$',
                Is_Active__c = true,
                Enabled__c = true,
                RecordTypeId = CCM_Constants.SYSTEM_CONFIGURATION_RECORD_TYPE_SN_FORMAT_ID
            )
        };
        insert lstSystemConfiguration;
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'EGO';
        insert acc;
        Product2 p = TestDataFactory.createProduct();
        Warranty__c w = TestDataFactory.createWarranty();
        w.AccountCustomer__c = acc.Id;
        w.Master_Product__c = p.Id;
        w.Brand_Name__c = 'Skil';
        w.Product_Use_Type2__c = 'Professional/Commercial';
        w.Lost_Receipt__c = true;
        insert w;
        Warranty__c w1 = TestDataFactory.createWarranty();
        w1.AccountCustomer__c = acc.Id;
        w1.Master_Product__c = p.Id;
        w1.Brand_Name__c = 'SkilSaw';
        w1.Product_Use_Type2__c = 'Professional/Commercial';
        w1.Lost_Receipt__c = true;
        insert w1;
        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(w.Id);
        wi.Serial_Number__c = 'NLM01190500004';
        insert wi;
        Case cs = TestDataFactory.createCase();
        cs.AccountId = acc.Id;
        insert cs;
        List<Warranty_Item__c> items = new List<Warranty_Item__c>{ TestDataFactory.createWarrantyItem(w.Id), TestDataFactory.createWarrantyItem(w.Id) };
        insert items;
        Test.stopTest();
    }
    @IsTest
    //Add by Abby on 4/4/2020
    static void testChangeMasterProduct4() {
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Product'];
        RecordType rtd = [SELECT Id FROM RecordType WHERE Name = 'Products and Parts' AND SobjectType = 'Kit_item__c' LIMIT 1];
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'Skil';
        acc.Phone = '********';
        acc.PersonEmail = '<EMAIL>';
        insert acc;
        List<Product2> prodList = new List<Product2>();
        Product2 masterProd = new Product2();
        masterProd.Name = 'Test';
        masterProd.Brand_Name__c = 'Skil';
        masterProd.RecordTypeId = kitRecordType[0].Id;
        masterProd.ProductCode = 'CB738401';
        masterProd.Country_of_Origin__c = 'United States';
        masterProd.IsActive = true;
        masterProd.Source__c = 'EBS';
        prodList.add(masterProd);
        Product2 kitItemProd1 = new Product2();
        kitItemProd1.Name = 'Test';
        kitItemProd1.Brand_Name__c = 'Skil';
        kitItemProd1.RecordTypeId = proRecordType[0].Id;
        kitItemProd1.ProductCode = 'BY519901';
        kitItemProd1.Country_of_Origin__c = 'United States';
        kitItemProd1.IsActive = true;
        kitItemProd1.Source__c = 'EBS';
        prodList.add(kitItemProd1);
        Product2 kitItemProd2 = new Product2();
        kitItemProd2.Name = 'Test';
        kitItemProd2.Brand_Name__c = 'Skil';
        kitItemProd2.RecordTypeId = proRecordType[0].Id;
        kitItemProd2.ProductCode = 'SC536501';
        kitItemProd2.Country_of_Origin__c = 'United States';
        kitItemProd2.IsActive = true;
        kitItemProd2.Source__c = 'EBS';
        prodList.add(kitItemProd2);
        Product2 kitItemProd3 = new Product2();
        kitItemProd3.Name = 'Test';
        kitItemProd3.Brand_Name__c = 'Skil';
        kitItemProd3.RecordTypeId = proRecordType[0].Id;
        kitItemProd3.ProductCode = 'BY519901T';
        kitItemProd3.Country_of_Origin__c = 'United States';
        kitItemProd3.IsActive = true;
        kitItemProd3.Source__c = 'EBS';
        prodList.add(kitItemProd3);
        insert prodList;
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>();
        Kit_Item__c kitItem1 = new Kit_Item__c();
        kitItem1.Kit__c = masterProd.Id;
        kitItem1.Product__c = kitItemProd1.Id;
        kitItem1.RecordTypeId = rtd.Id;
        kitItem1.Source__c = 'EBS';
        kitItem1.Sequence__c = 'test';
        kitItem1.Repairable__c = true;
        kitItemList.add(kitItem1);
        Kit_Item__c kitItem2 = new Kit_Item__c();
        kitItem2.Kit__c = masterProd.Id;
        kitItem2.Product__c = kitItemProd2.Id;
        kitItem2.RecordTypeId = rtd.Id;
        kitItem2.Source__c = 'EBS';
        kitItem2.Repairable__c = true;
        kitItemList.add(kitItem2);
        Kit_Item__c kitItem3 = new Kit_Item__c();
        kitItem3.Kit__c = masterProd.Id;
        kitItem3.Product__c = kitItemProd3.Id;
        kitItem3.RecordTypeId = rtd.Id;
        kitItem3.Source__c = 'EBS';
        kitItem3.Sequence__c = 'test';
        kitItem3.Repairable__c = true;
        kitItemList.add(kitItem3);
        insert kitItemList;
        Warranty__c warrantyInfo = new Warranty__c();
        warrantyInfo.AccountCustomer__c = acc.Id;
        warrantyInfo.Master_Product__c = masterProd.Id;
        warrantyInfo.Brand_Name__c = 'Skil';
        warrantyInfo.Product_Use_Type2__c = 'Professional/Commercial';
        warrantyInfo.Lost_Receipt__c = true;
        insert warrantyInfo;
        List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>();
        Warranty_Item__c wItem1 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem1.Product__c = kitItemProd1.Id;
        wItem1.Serial_Number__c = 'NLM01190500001';
        warrantyItemList.add(wItem1);
        Warranty_Item__c wItem2 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem2.Product__c = kitItemProd1.Id;
        wItem2.Serial_Number__c = 'NLM01190500002';
        warrantyItemList.add(wItem2);
        Warranty_Item__c wItem3 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem3.Product__c = kitItemProd1.Id;
        wItem3.Serial_Number__c = 'NLM01190500003';
        warrantyItemList.add(wItem3);
        insert warrantyItemList;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(masterProd.Id, warrantyInfo.Id);
        //CCM_WarrantyController.getProductIfHaveSurvey(kitItemProd2.Id);
        try {
            //CCM_WarrantyController.getProductIfHaveSurvey('');
        } catch (Exception e) {
        }
        Test.stopTest();
    }
    @IsTest
    //Add by Abby on 4/4/2020
    static void testChangeMasterProduct5() {
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType WHERE SobjectType = 'Product2' AND DeveloperName = 'Product'];
        RecordType rtd = [SELECT Id FROM RecordType WHERE Name = 'Products and Parts' AND SobjectType = 'Kit_item__c' LIMIT 1];
        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'Skil';
        acc.Phone = '********';
        acc.PersonEmail = '<EMAIL>';
        insert acc;
        List<Product2> prodList = new List<Product2>();
        Product2 masterProd = new Product2();
        masterProd.Name = 'Test';
        masterProd.Brand_Name__c = 'Skil';
        masterProd.RecordTypeId = kitRecordType[0].Id;
        masterProd.ProductCode = 'CB738401';
        masterProd.Country_of_Origin__c = 'United States';
        masterProd.IsActive = true;
        masterProd.Source__c = 'EBS';
        prodList.add(masterProd);
        Product2 kitItemProd1 = new Product2();
        kitItemProd1.Name = 'Test';
        kitItemProd1.Brand_Name__c = 'Skil';
        kitItemProd1.RecordTypeId = proRecordType[0].Id;
        kitItemProd1.ProductCode = 'BY519901';
        kitItemProd1.Country_of_Origin__c = 'United States';
        kitItemProd1.IsActive = true;
        kitItemProd1.Source__c = 'EBS';
        prodList.add(kitItemProd1);
        Product2 kitItemProd2 = new Product2();
        kitItemProd2.Name = 'Test';
        kitItemProd2.Brand_Name__c = 'Skil';
        kitItemProd2.RecordTypeId = proRecordType[0].Id;
        kitItemProd2.ProductCode = 'SC536501';
        kitItemProd2.Country_of_Origin__c = 'United States';
        kitItemProd2.IsActive = true;
        kitItemProd2.Source__c = 'EBS';
        prodList.add(kitItemProd2);
        Product2 kitItemProd3 = new Product2();
        kitItemProd3.Name = 'Test';
        kitItemProd3.Brand_Name__c = 'Skil';
        kitItemProd3.RecordTypeId = proRecordType[0].Id;
        kitItemProd3.ProductCode = 'BY519901T';
        kitItemProd3.Country_of_Origin__c = 'United States';
        kitItemProd3.IsActive = true;
        kitItemProd3.Source__c = 'EBS';
        prodList.add(kitItemProd3);
        insert prodList;
        List<Kit_Item__c> kitItemList = new List<Kit_Item__c>();
        Kit_Item__c kitItem1 = new Kit_Item__c();
        kitItem1.Kit__c = masterProd.Id;
        kitItem1.Product__c = kitItemProd1.Id;
        kitItem1.RecordTypeId = rtd.Id;
        kitItem1.Source__c = 'EBS';
        kitItem1.Sequence__c = 'test';
        kitItem1.Repairable__c = true;
        kitItemList.add(kitItem1);
        Kit_Item__c kitItem2 = new Kit_Item__c();
        kitItem2.Kit__c = masterProd.Id;
        kitItem2.Product__c = kitItemProd2.Id;
        kitItem2.RecordTypeId = rtd.Id;
        kitItem2.Source__c = 'EBS';
        kitItem2.Repairable__c = true;
        kitItemList.add(kitItem2);
        Kit_Item__c kitItem3 = new Kit_Item__c();
        kitItem3.Kit__c = masterProd.Id;
        kitItem3.Product__c = kitItemProd3.Id;
        kitItem3.RecordTypeId = rtd.Id;
        kitItem3.Source__c = 'EBS';
        kitItem3.Sequence__c = 'test';
        kitItem3.Repairable__c = true;
        kitItemList.add(kitItem3);
        insert kitItemList;
        Warranty__c warrantyInfo = new Warranty__c();
        warrantyInfo.AccountCustomer__c = acc.Id;
        warrantyInfo.Master_Product__c = masterProd.Id;
        warrantyInfo.Brand_Name__c = 'Skil';
        warrantyInfo.Product_Use_Type2__c = 'Professional/Commercial';
        warrantyInfo.Lost_Receipt__c = true;
        insert warrantyInfo;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(masterProd.Id, warrantyInfo.Id);
        Test.stopTest();
    }
    @IsTest
    static void testInit() {
        Account acc = [SELECT Id, Product_Type__c FROM Account LIMIT 1];
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];
        Case cs = [SELECT Id FROM Case LIMIT 1];
        List<Warranty_Item__c> items = new List<Warranty_Item__c>{ TestDataFactory.createWarrantyItem(w.Id), TestDataFactory.createWarrantyItem(w.Id) };
        insert items;
        Test.startTest();
        CCM_WarrantyController.init(acc.Id, '', '');
        CCM_WarrantyController.init('', cs.Id, '');
        CCM_WarrantyController.init('', '', w.Id);
        CCM_WarrantyController.isCustomerBrand(acc.Id, acc.Product_Type__c);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id FROM Warranty__c LIMIT 1];
        Warranty__c w1 = [SELECT Id FROM Warranty__c WHERE Brand_Name__c = 'SkilSaw' LIMIT 1];
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct('', w.Id);
        CCM_WarrantyController.changeMasterProduct(p.Id, w1.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct2() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id, Brand_Name__c FROM Warranty__c LIMIT 1];
        w.Brand_Name__c = 'SkilSaw';
        update w;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeMasterProduct3() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty__c w = [SELECT Id, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        w.Brand_Name__c = 'EGO';
        update w;
        Test.startTest();
        CCM_WarrantyController.changeMasterProduct(p.Id, w.Id);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Product_Code__c = '*****************';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator3() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = '11';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator4() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'RLM201902000001X';
        wi.Product_Code__c = '*****************';
        update wi;
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'EGO', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator1() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'Skil', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator2() {
        Product2 p = [SELECT Id FROM Product2 LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('1121', pplist);
        CCM_WarrantyController.checkSNAndUpdateIndicator(String.valueOf(p.Id), String.valueOf(Date.today()), 'Professional/Commercial', 'ACE', 'SkilSaw', JSON.serialize(wiw), false);
        Test.stopTest();
    }
    @IsTest
    static void testSetWarrantyItemExpirationDateForSkilAndSkilSaw() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        Map<String, Decimal> product_warrantyYear_map = new Map<String, Decimal>();
        CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, wi, product_warrantyYear_map);
        Test.stopTest();
    }
    @IsTest
    static void testSetWarrantyItemExpirationDateForSkilAndSkilSaw1() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Brand_Name__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        w.Product_Use_Type2__c = 'Residential';
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c, Product__c, Product_Model__c, Product_Type__c FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        Map<String, Decimal> product_warrantyYear_map = new Map<String, Decimal>();
        CCM_WarrantyController.setWarrantyItemExpirationDateForSkilAndSkilSaw(w, wi, product_warrantyYear_map);
        CCM_WarrantyController.setWarrantyItemExpirationDateForEGO(w, wi);
        Test.stopTest();
    }
    @IsTest
    static void testSave() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testSave1() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'N130800001';
        update wi;
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testSave2() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id, Serial_Number__c FROM Warranty_Item__c LIMIT 1];
        wi.Serial_Number__c = 'RNLM041806 00001X';
        update wi;
        Case ca = [SELECT Id FROM Case LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.save(JSON.serialize(w), JSON.serialize(wiw), ca.Id);
        Test.stopTest();
    }
    @IsTest
    static void testChangeExpirationDate() {
        Warranty__c w = [SELECT Id, Purchase_Date__c, Product_Use_Type2__c, Lost_Receipt__c FROM Warranty__c LIMIT 1];
        Warranty_Item__c wi = [SELECT Id FROM Warranty_Item__c LIMIT 1];
        Test.startTest();
        CCM_WarrantyController.Product3 pp = new CCM_WarrantyController.Product3(wi);
        List<CCM_WarrantyController.Product3> pplist = new List<CCM_WarrantyController.Product3>();
        pplist.add(pp);
        CCM_WarrantyController.WarrantyItemWrapper wiw = new CCM_WarrantyController.WarrantyItemWrapper('*********', pplist);
        CCM_WarrantyController.changeExpirationDate(w, JSON.serialize(wiw));
        Test.stopTest();
    }
    @IsTest
    static void testSaveSurvey() {
        Test.startTest();
        Chervon_Survey__c objSurvey = new Chervon_Survey__c(Name = 'test');
        insert objSurvey;
        List<Chervon_Survey_Question__c> lstQuestion = new List<Chervon_Survey_Question__c>{
            new Chervon_Survey_Question__c(Chervon_Survey__c = objSurvey.Id, Question__c = 'test1', Type__c = 'Free Text', Required__c = true),
            new Chervon_Survey_Question__c(Chervon_Survey__c = objSurvey.Id, Question__c = 'test2', Type__c = 'Multi Select', Required__c = true)
        };
        insert lstQuestion;
        List<Chervon_Survey_Question_Choice__c> lstChoice = new List<Chervon_Survey_Question_Choice__c>{
            new Chervon_Survey_Question_Choice__c(Chervon_Survey_Question__c = lstQuestion[0].Id, Required__c = true, Choice__c = 'test1'),
            new Chervon_Survey_Question_Choice__c(Chervon_Survey_Question__c = lstQuestion[1].Id, Required__c = true, Choice__c = 'test2')
        };
        insert lstChoice;
        Account objAccount = new Account(Name = 'test');
        objAccount.TaxID__c = 'testTax';
        insert objAccount;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objAccount.Id);
        insert objWarranty;
        CCM_WarrantyController.ChoiceWrapper objChoiceWrapper1 = new CCM_WarrantyController.ChoiceWrapper();
        objChoiceWrapper1.id = lstChoice[0].Id;
        objChoiceWrapper1.checked = true;
        objChoiceWrapper1.answerText = 'test';
        CCM_WarrantyController.ChoiceWrapper objChoiceWrapper2 = new CCM_WarrantyController.ChoiceWrapper();
        objChoiceWrapper2.id = lstChoice[1].Id;
        objChoiceWrapper2.checked = true;
        objChoiceWrapper2.answerText = 'test';
        CCM_WarrantyController.QuestionChoiceWrapper objQuestionChoiceWrapper1 = new CCM_WarrantyController.QuestionChoiceWrapper();
        objQuestionChoiceWrapper1.id = lstQuestion[0].Id;
        objQuestionChoiceWrapper1.type = 'Free Text';
        objQuestionChoiceWrapper1.choices = new List<CCM_WarrantyController.ChoiceWrapper>{ objChoiceWrapper1 };
        CCM_WarrantyController.QuestionChoiceWrapper objQuestionChoiceWrapper2 = new CCM_WarrantyController.QuestionChoiceWrapper();
        objQuestionChoiceWrapper2.id = lstQuestion[0].Id;
        objQuestionChoiceWrapper2.type = 'Multi Select';
        objQuestionChoiceWrapper2.choices = new List<CCM_WarrantyController.ChoiceWrapper>{ objChoiceWrapper2 };
        List<CCM_WarrantyController.QuestionChoiceWrapper> lstQuestionChoiceWrapper = new List<CCM_WarrantyController.QuestionChoiceWrapper>{
            objQuestionChoiceWrapper1,
            objQuestionChoiceWrapper2
        };
        try {
            CCM_WarrantyController.saveSurvey(objSurvey.Id, objAccount.Id, objWarranty.Id, JSON.serialize(lstQuestionChoiceWrapper));
        } catch (Exception objE) {
            System.assertEquals(objE.getMessage(), 'test');
        }
        Test.stopTest();
    }
    @IsTest
    static void testGetProductIfHaveSurvey() { 
        Test.startTest();
        Chervon_Survey__c objSurvey = new Chervon_Survey__c(Name = 'test');
        insert objSurvey;
        Product2 objProduct = new Product2(Name = 'test', Chervon_Survey__c = objSurvey.Id);
        insert objProduct;
        Account acc = [SELECT Id FROM Account LIMIT 1];
        CCM_WarrantyController.getProductIfHaveSurvey(objProduct.Id);
        Test.stopTest();
    }
    @IsTest
    static void testFindProjectsAccordingSerialNum() {
        Product2 p2 = new Product2();
        p2.Name = 'testProduct';
        p2.ProductCode = '1111';
        p2.RecordTypeId = QueryUtils.getRecordTypeIdBySobjectTypeAndDeveloperName('Product2', 'Product');
        insert p2;
        Project__c pro = new Project__c();
        pro.Product__c = p2.Id;
        pro.Email_Template_Developer_Name__c = 'test';
        pro.Send_Email__c = false;
        pro.Deadline__c = Date.today() + 1;
        pro.Brand_Name__c = 'Skil';
        insert pro;
        Project_SN__c psn = new Project_SN__c();
        psn.Project__c = pro.Id;
        psn.Star_SN__c = 'psn start';
        psn.End_SN__c = 'psn end';
        insert psn;
        Test.startTest();
        CCM_WarrantyController.findProjectsAccordingSerialNum('*********', '1111', 'Skil');
        Test.stopTest();
    }
    @IsTest
    static void testGetAccountSiteOriginOutsideEuropeStatus() {
        Test.startTest();
        List<Account> lstAccount = new List<Account>{
            new Account(Name = 'Test1', Site_Origin__c = 'United State', TaxID__c = 'testTax'),
            new Account(Name = 'Test1', Site_Origin__c = 'United Kingdom', TaxID__c = 'testTax')
        };
        insert lstAccount;
        System.assert(CCM_WarrantyController.getAccountSiteOriginOutsideEuropeStatus(lstAccount[0].Id) == true);
        System.assert(CCM_WarrantyController.getAccountSiteOriginOutsideEuropeStatus(lstAccount[1].Id) == false);
        Test.stopTest();
    }
    @IsTest
    static void test3DigitsHammerheadSN() {
        Test.startTest();
        Account objEndUserAccount = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndUserAccount;
        Product2 objProduct = new Product2(Name = 'test', Brand_Name__c = 'Hammerhead', IsActive = true);
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndUserAccount.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Serial_Number__c = '112', Product_Code__c = '*****************');
        String strResult = CCM_WarrantyController.checkSNAndUpdateIndicator(
            objProduct.Id,
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'ACE',
            'Hammerhead',
            JSON.serialize(
                new CCM_WarrantyController.WarrantyItemWrapper('1121', new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) })
            ),
            false
        );
        Test.stopTest();
        System.assertEquals(true, strResult != null && strResult.startsWith('{"proList":[{"warrantyItem":{"attributes":{"type":"Warranty_Item__c"}'));
    }
    @IsTest
    static void testGetEuropeanProduct() {
        Test.startTest();
        Account objEndCustomer = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndCustomer;
        Product2 objProduct = new Product2(
            Name = 'test',
            IsActive = true,
            Country_of_Origin__c = Label.CCM_Product_Country_Origin_European_List.split(CCM_Constants.SEMICOLON_REGEXP_SPACE_IGNORED)[0]
        );
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndCustomer.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Product__c = objProduct.Id);
        insert objWarrantyItem;
        Set<Id> setEuProduct = CCM_WarrantyController.getEuropeanProduct(new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) });
        Test.stopTest();
        System.assertEquals(1, setEuProduct.size());
    }
    @IsTest
    static void testCheckSNAndUpdateIndicator4Flex() {
        Test.startTest();
        Account objEndCustomer = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        insert objEndCustomer;
        Product2 objProduct = new Product2(Name = 'test', IsActive = true, Brand_Name__c = 'Flex');
        insert objProduct;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndCustomer.Id);
        insert objWarranty;
        Warranty_Item__c objWarrantyItem = new Warranty_Item__c(Warranty__c = objWarranty.Id, Product__c = objProduct.Id, Serial_Number__c = '*************', Product_Code__c = '*****************');
        insert objWarrantyItem;
        CCM_WarrantyController.checkSNAndUpdateIndicator(
            objProduct.Id,
            String.valueOf(Date.today()),
            'Professional/Commercial',
            'ACE',
            'FLEX',
            JSON.serialize(
                new CCM_WarrantyController.WarrantyItemWrapper('test', new List<CCM_WarrantyController.Product3>{ new CCM_WarrantyController.Product3(objWarrantyItem) })
            ),
            false
        );
        Test.stopTest();
    }
}