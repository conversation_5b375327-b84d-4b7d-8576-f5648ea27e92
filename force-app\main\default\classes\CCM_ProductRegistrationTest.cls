@isTest
public class CCM_ProductRegistrationTest {
    static testMethod void testMethod0() {
        test.startTest();
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType 
											WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'] ;
        List<RecordType> acRecordType1 = [SELECT Id, Name FROM RecordType 
											WHERE SObjectType = 'Account' AND Name = 'Channel'] ;
        
        Account cac = new Account();
        cac.Name = 'test';
        cac.ShippingPostalCode = '60007';
        cac.RecordTypeId = acRecordType1[0].Id;
        cac.Distributor_or_Dealer__c = 'Dealer';
        cac.TaxID__c = 'testTax';
        //CCM_SalesOrgValidationHandler.isRun = false;
        insert cac;
        
        
        Account ac = new Account();
        ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;
        
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> partRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'];
    
        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;
    
        Product2 pkit = new Product2();
        pkit.Name = 'Test';
        pkit.Brand_Name__c = 'EGO';
        pkit.RecordTypeId = kitRecordType[0].Id;
        pkit.ProductCode = '1234567';
        pkit.IsActive = true;
        pkit.Source__c = 'PIM';
        insert pkit;

        Product2 par = new Product2();
        par.Name = 'Test1';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;
    
        Recordtype rtd = [SELECT id FROM RecordType WHERE Name = 'Products and Parts' and sobjecttype = 'Kit_item__c' LIMIT 1];
        Kit_item__c ki1 = new Kit_item__c();
        ki1.product__c = pro.Id;
        ki1.Parts__c = par.Id;
        ki1.RecordTypeId = rtd.id;
        ki1.Source__c= 'PIM';
        ki1.Sequence__c = 'test';
        ki1.Repairable__c = true;
        
        insert ki1;
    
        Recordtype rtd2 = [SELECT id FROM RecordType WHERE Name = 'Kits and Products' and sobjecttype = 'Kit_item__c' LIMIT 1];
        Kit_item__c ki2 = new Kit_item__c();
        ki2.product__c = pro.Id;
        ki2.Kit__c = pkit.Id;
        ki2.RecordTypeId = rtd2.id;
        ki2.Source__c= 'PIM';
        ki2.Sequence__c = 'test';
        ki2.Repairable__c = true;
        insert ki2;
    
        

        BOM__c bom = new BOM__c();
        bom.Product_code__c = pro.ProductCode;
        bom.Parts_code__c = pro.ProductCode;
        bom.Quantity__c = 1;
        insert bom;

        Storage__c sto = new Storage__c();
        sto.Product__c = pro.Id;
        sto.Available_Inventory__c = 2;
        sto.Sub_storage__c = 'CNA01';
        insert sto;

        Storage__c sto2 = new Storage__c();
        sto2.Product__c = par.Id;
        sto2.Available_Inventory__c = 2;
        sto2.Sub_storage__c = 'CNA01';
        insert sto2;

        Storage_List__c sl = new Storage_List__c();
        sl.Name = 'CNA01';
        sl.Postal_Code__c = '91708';
        insert sl;

        Pricebook2 pb = new Pricebook2();
        pb.IsActive = true;
        pb.Name = 'Test';
        insert pb;
    
        Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
        cbpm.Type__c = 'Service';
        cbpm.Name = 'CNA-Direct Dealer Price for Parts';
        cbpm.Price_Book__c = pb.Id;
        insert cbpm;
            
        Default_PriceBook__c dpb = new Default_PriceBook__c();
        dpb.Price_Book_Id__c = pb.Id;
        dpb.Name = 'defaultBook';
        dpb.DevelopName__c = 'Warranty and Sample price list';
        insert dpb;

        ID standardPBID = Test.getStandardPricebookId(); 

        /*PricebookEntry standardpbe = new PricebookEntry();
        standardpbe.Pricebook2Id = standardPBID;
        standardpbe.Product2Id = pro.Id;
        standardpbe.UnitPrice = 1000;
        standardpbe.IsActive = true;
        insert standardpbe;*/

        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;
    
        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = ac.Id;
        wrty.Purchase_Date__c = Date.today();
        insert wrty;
        
        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = 'NLM0219080001X';
        insert wi;
        Test.setMock(HttpCalloutMock.class, new HttpCalloutsMock());
        CCM_ProductRegistration.SearchPartsByProduct(pro.Id, 'Test');
        try {
            CCM_ProductRegistration.getAddressByCode('60007','US');
        } catch (Exception e) {
            
        } 
            
        test.stopTest();
    }

    static testMethod void testMethods() {
        
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType 
											WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'] ;
        List<RecordType> acRecordType1 = [SELECT Id, Name FROM RecordType 
											WHERE SObjectType = 'Account' AND Name = 'Channel'] ;
        
        Account cac = new Account();
        cac.Name = 'test';
        cac.ShippingPostalCode = '60007';
        cac.RecordTypeId = acRecordType1[0].Id;
        cac.Distributor_or_Dealer__c = 'Dealer';
        cac.TaxID__c = 'testTax';
        //CCM_SalesOrgValidationHandler.isRun = false;
        insert cac;
        
        
        Account ac = new Account();
        ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;
        
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> partRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'];
    
        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;
    
        Product2 pkit = new Product2();
        pkit.Name = 'Test';
        pkit.Brand_Name__c = 'EGO';
        pkit.RecordTypeId = kitRecordType[0].Id;
        pkit.ProductCode = '1234567';
        pkit.IsActive = true;
        pkit.Source__c = 'PIM';
        insert pkit;

        Product2 par = new Product2();
        par.Name = 'Test1';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;
    
        Recordtype rtd = [SELECT id FROM RecordType WHERE Name = 'Products and Parts' and sobjecttype = 'Kit_item__c' LIMIT 1];
        Kit_item__c ki1 = new Kit_item__c();
        ki1.product__c = pro.Id;
        ki1.Parts__c = par.Id;
        ki1.RecordTypeId = rtd.id;
        ki1.Source__c= 'PIM';
        ki1.Sequence__c = 'test';
        ki1.Repairable__c = true;
        
        insert ki1;
    
        Recordtype rtd2 = [SELECT id FROM RecordType WHERE Name = 'Kits and Products' and sobjecttype = 'Kit_item__c' LIMIT 1];
        Kit_item__c ki2 = new Kit_item__c();
        ki2.product__c = pro.Id;
        ki2.Kit__c = pkit.Id;
        ki2.RecordTypeId = rtd2.id;
        ki2.Source__c= 'PIM';
        ki2.Sequence__c = 'test';
        ki2.Repairable__c = true;
        insert ki2;
    
        

        BOM__c bom = new BOM__c();
        bom.Product_code__c = pro.ProductCode;
        bom.Parts_code__c = pro.ProductCode;
        bom.Quantity__c = 1;
        insert bom;

        Storage__c sto = new Storage__c();
        sto.Product__c = pro.Id;
        sto.Available_Inventory__c = 2;
        sto.Sub_storage__c = 'CNA01';
        insert sto;

        Storage__c sto2 = new Storage__c();
        sto2.Product__c = par.Id;
        sto2.Available_Inventory__c = 2;
        sto2.Sub_storage__c = 'CNA01';
        insert sto2;

        Storage_List__c sl = new Storage_List__c();
        sl.Name = 'CNA01';
        sl.Postal_Code__c = '91708';
        insert sl;

        Pricebook2 pb = new Pricebook2();
        pb.IsActive = true;
        pb.Name = 'Test';
        insert pb;
    
        Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
        cbpm.Type__c = 'Service';
        cbpm.Name = 'CNA-Direct Dealer Price for Parts';
        cbpm.Price_Book__c = pb.Id;
        insert cbpm;
            
        Default_PriceBook__c dpb = new Default_PriceBook__c();
        dpb.Price_Book_Id__c = pb.Id;
        dpb.Name = 'defaultBook';
        dpb.DevelopName__c = 'Warranty and Sample price list';
        insert dpb;

        ID standardPBID = Test.getStandardPricebookId(); 

        /*PricebookEntry standardpbe = new PricebookEntry();
        standardpbe.Pricebook2Id = standardPBID;
        standardpbe.Product2Id = pro.Id;
        standardpbe.UnitPrice = 1000;
        standardpbe.IsActive = true;
        insert standardpbe;*/

        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;
    
        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = ac.Id;
        wrty.Purchase_Date__c = Date.today();
        insert wrty;
        
        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = 'NLM0219080001X';
        wi.Product_Code__c = 'FC12';
        insert wi;
        test.startTest();
        CCM_ProductRegistration.setupProductRegistration(ac.Id);
        CCM_ProductRegistration.customerInfo('<EMAIL>');
        CCM_ProductRegistration.customerInfo('<EMAIL>','1111111');
        CCM_ProductRegistration.WarrantyMasterProduct('EGO','Test');
        CCM_ProductRegistration.changeMasterProduct(pkit.Id);
        CCM_ProductRegistration.Product3 p3 = new CCM_ProductRegistration.Product3(wi);
        p3.isSelect = true;
        List<CCM_ProductRegistration.Product3> p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        CCM_ProductRegistration.WarrantyItemWrapper wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', Json.serialize(wiNew)) ;
        CCM_ProductRegistration.findProjectsAccordingSerialNum('NLM0219080001X','1234567' , 'EGO');
        //String body = '{"FirstName":"yizhao","LastName":"Xu","PersonEmail":"<EMAIL>","ShippingStreet":"13 oak drive","Phone":"","ShippingPostalCode":"13346","ShippingCity":"Hamilton","ShippingState":"NY","purchaseDate":"2019-12-03","brand":"EGO","masterProduct":"'+ pkit.Id +'","purchasePlace":"EGO Partner","purchaseUseType":"Industrial/Professional/Commercial","proListStr":"{"proList":[{"warrantyItem":{"attributes":{"type":"Warranty_Item__c"},"Product_Code__c":"HT2410","Product__c":"'+pro.Id+'","Product_Name__c":"56V Lithium-Ion Cordless 24" Brushless Hedge Trimmer","Product_Model__c":"2","Serial_Number__c":"NLM20190400001X","Product_Type__c":"Product","Warranty__c":null},"snFormatErrorMessage":"","isSelect":true,"isFormatCorrect":true,"inRecallProject":false}],"productCode":"HT2410"}","lostReceipt":false,"ContentId":""}';
        ac.PersonEmail = '';
        
        CCM_ProductRegistration.Product3 product3 = new CCM_ProductRegistration.Product3(wi);
        List<CCM_ProductRegistration.Product3> poList = new List<CCM_ProductRegistration.Product3>();
        poList.add(product3);
        CCM_ProductRegistration.WarrantyItemWrapper itemWrapper = new CCM_ProductRegistration.WarrantyItemWrapper(par.ProductCode, poList);
        Map<String, Object> paramMap = new Map<String, Object>();
        paramMap.put('FirstName', ac.FirstName);
        paramMap.put('LastName', ac.LastName);
        paramMap.put('PersonEmail', ac.PersonEmail);
        paramMap.put('proListStr', JSON.serialize(itemWrapper));
        CCM_ProductRegistration.SaveWarranty(JSON.serialize(paramMap));
        CCM_ProductRegistration.uploadFile('11', '');

        CCM_ProductRegistration.PurchasePlacePicklist('Test',ac.Id,'Skil');
        
        test.stopTest();
        CCM_ProductRegistration.SearchPartsListByProduct(pro.Id, 'Test');
        CCM_ProductRegistration.SearchProduct('Test');
        CCM_ProductRegistration.SearchModelNumber('1234567');
        
        CCM_ProductRegistration.GeneratePartsList(pro.Id,'1234567');     
    }

    static testMethod void testMethods1() {
        
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType 
											WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'] ;
        Account ac = new Account();
        ac.LastName = 'test';
        ac.FirstName = 'test';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;
        
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> partRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'];
    
        Product2 pro = new Product2();
        pro.Name = 'Test';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;
    
        Product2 pkit = new Product2();
        pkit.Name = 'Test';
        pkit.Brand_Name__c = 'EGO';
        pkit.RecordTypeId = kitRecordType[0].Id;
        pkit.ProductCode = '1234567';
        pkit.IsActive = true;
        pkit.Source__c = 'PIM';
        insert pkit;

        Product2 par = new Product2();
        par.Name = 'Test1';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;
    
        Recordtype rtd = [SELECT id FROM RecordType WHERE Name = 'Products and Parts' and sobjecttype = 'Kit_item__c' LIMIT 1];
        Kit_item__c ki1 = new Kit_item__c();
        ki1.product__c = pro.Id;
        ki1.Parts__c = par.Id;
        ki1.RecordTypeId = rtd.id;
        ki1.Source__c= 'PIM';
        ki1.Repairable__c = true;
        ki1.Wearable_Parts__c = true;
        ki1.Warranty_Day__c = '2';
        insert ki1;
    
        

        BOM__c bom = new BOM__c();
        bom.Product_code__c = pro.ProductCode;
        bom.Parts_code__c = pro.ProductCode;
        bom.Quantity__c = 1;
        insert bom;

        Storage__c sto = new Storage__c();
        sto.Product__c = pro.Id;
        sto.Available_Inventory__c = 2;
        sto.Sub_storage__c = 'CNA01';
        insert sto;

        Storage__c sto2 = new Storage__c();
        sto2.Product__c = par.Id;
        sto2.Available_Inventory__c = 2;
        sto2.Sub_storage__c = 'CNA01';
        insert sto2;

        Storage_List__c sl = new Storage_List__c();
        sl.Name = 'CNA01';
        sl.Postal_Code__c = '91708';
        insert sl;

        Pricebook2 pb = new Pricebook2();
        pb.IsActive = true;
        pb.Name = 'Test';
        insert pb;
    
        Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
        cbpm.Type__c = 'Service';
        cbpm.Name = 'CNA-Direct Dealer Price for Parts';
        cbpm.Price_Book__c = pb.Id;
        insert cbpm;
            
        Default_PriceBook__c dpb = new Default_PriceBook__c();
        dpb.Price_Book_Id__c = pb.Id;
        dpb.Name = 'defaultBook';
        dpb.DevelopName__c = 'Warranty and Sample price list';
        insert dpb;

        ID standardPBID = Test.getStandardPricebookId(); 

        /*PricebookEntry standardpbe = new PricebookEntry();
        standardpbe.Pricebook2Id = standardPBID;
        standardpbe.Product2Id = pro.Id;
        standardpbe.UnitPrice = 1000;
        standardpbe.IsActive = true;
        insert standardpbe;*/

        PricebookEntry pbe = new PricebookEntry();
        pbe.IsActive = true;
        pbe.Product2Id = pro.Id;
        pbe.UnitPrice = 1000;
        pbe.Pricebook2Id = pb.Id;
        pbe.UseStandardPrice = false;
        insert pbe;
    
        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = ac.Id;
        wrty.Purchase_Date__c = Date.today();
        insert wrty;
        
        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = 'NLM0219080001X';
        insert wi;
        test.startTest();
      ac.PersonEmail = '<EMAIL>';
        CCM_ProductRegistration.SaveWarranty(JSON.serialize(ac));
		CCM_ProductRegistration.changeMasterProduct('');
        ContentVersion conVer = new ContentVersion();
        conVer.ContentLocation = 'S'; // S specify this document is in SF, use E for external files
        conVer.PathOnClient = '11'; // The files name, extension is very important here which will help the file in preview.
        conVer.Title = 'Receipt '+ String.valueOf(Datetime.now()); // Display name of the files
        conVer.VersionData = EncodingUtil.base64Decode('11');
        insert conVer;
        List<ContentVersion> cv = [SELECT Id,contentDocumentId FROM ContentVersion];
        CCM_ProductRegistration.uploadFile('11', '11');
        CCM_ProductRegistration.deleteFile(cv[0].Id);
        CCM_ProductRegistration.deleteFile('');
        // CCM_ProductRegistration.SearchPartsByProduct(pro.Id, 'Test');
        test.stopTest();     
    }
    
    static testMethod void testMethods2() {
        User admin = [SELECT Id, Username, UserRoleId FROM User WHERE Profile.Name = 'System Administrator'  AND UserRoleId != null AND IsActive = true LIMIT 1];
        admin.Country_of_Origin__c = 'Global';
		update admin;
        
        System.runAs(admin){
            List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'] ;
            Account ac = new Account();
            ac.LastName = 'test';
            ac.FirstName = 'test';
            ac.ShippingPostalCode = '60007';
            ac.RecordTypeId = acRecordType[0].Id;
            ac.PersonEmail = '<EMAIL>';
            ac.Distributor_or_Dealer__c = 'Dealer';
            ac.TaxID__c = 'testTax';
            insert ac;
            
            List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                    WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];
            List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType 
                                                    WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'];
            List<RecordType> partRecordType = [SELECT Id, Name FROM RecordType 
                                                    WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'];
        
            Product2 pro = new Product2();
            pro.Name = 'Test';
            pro.Brand_Name__c = 'EGO';
            pro.RecordTypeId = proRecordType[0].Id;
            pro.ProductCode = '1234567';
            pro.IsActive = true;
            pro.Source__c = 'PIM';
            insert pro;
        
            Product2 pkit = new Product2();
            pkit.Name = 'Test';
            pkit.Brand_Name__c = 'EGO';
            pkit.RecordTypeId = kitRecordType[0].Id;
            pkit.ProductCode = '1234567';
            pkit.IsActive = true;
            pkit.Source__c = 'PIM';
            insert pkit;
    
            Product2 par = new Product2();
            par.Name = 'Test1';
            par.Brand_Name__c = 'EGO';
            par.RecordTypeId = partRecordType[0].Id;
            par.ProductCode = pro.ProductCode;
            par.IsActive = true;
            par.Source__c = 'PIM';
            insert par;
        
            Recordtype rtd = [SELECT id FROM RecordType WHERE Name = 'Products and Parts' and sobjecttype = 'Kit_item__c' LIMIT 1];
            Kit_item__c ki1 = new Kit_item__c();
            ki1.product__c = pro.Id;
            ki1.Parts__c = par.Id;
            ki1.RecordTypeId = rtd.id;
            ki1.Source__c= 'PIM';
            ki1.Repairable__c = true;
            insert ki1;
        
            test.startTest();
    
            BOM__c bom = new BOM__c();
            bom.Product_code__c = pro.ProductCode;
            bom.Parts_code__c = pro.ProductCode;
            bom.Quantity__c = 1;
            insert bom;
    
            Storage__c sto = new Storage__c();
            sto.Product__c = pro.Id;
            sto.Available_Inventory__c = 2;
            sto.Sub_storage__c = 'CNA01';
            insert sto;
    
            Storage__c sto2 = new Storage__c();
            sto2.Product__c = par.Id;
            sto2.Available_Inventory__c = 2;
            sto2.Sub_storage__c = 'CNA01';
            insert sto2;
    
            Storage_List__c sl = new Storage_List__c();
            sl.Name = 'CNA01';
            sl.Postal_Code__c = '91708';
            insert sl;
    
            Pricebook2 pb = new Pricebook2();
            pb.IsActive = true;
            pb.Name = 'Test';
            insert pb;
        
            Customer_Brand_Pricebook_Mapping__c cbpm = new Customer_Brand_Pricebook_Mapping__c();
            cbpm.Type__c = 'Service';
            cbpm.Name = 'CNA-Direct Dealer Price for Parts';
            cbpm.Price_Book__c = pb.Id;
            insert cbpm;
                
            Default_PriceBook__c dpb = new Default_PriceBook__c();
            dpb.Price_Book_Id__c = pb.Id;
            dpb.Name = 'defaultBook';
            dpb.DevelopName__c = 'Warranty and Sample price list';
            insert dpb;
    
            ID standardPBID = Test.getStandardPricebookId(); 
    
            PricebookEntry pbe = new PricebookEntry();
            pbe.IsActive = true;
            pbe.Product2Id = pro.Id;
            pbe.UnitPrice = 1000;
            pbe.Pricebook2Id = pb.Id;
            pbe.UseStandardPrice = false;
            insert pbe;
        
            Warranty__c wrty = TestDataFactory.createWarranty();
            wrty.Master_Product__c = pro.Id;
            wrty.AccountCustomer__c = ac.Id;
            wrty.Purchase_Date__c = Date.today();
            insert wrty;
            
            Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
            wi.Product__c = par.Id;
            wi.Serial_Number__c = 'NLM0219080001X';
            wi.Product_Code__c = 'FC12';
            insert wi;

            test.stopTest();
    
            CCM_ProductRegistration.WarrantyMasterProduct('EGO', '');
            CCM_ProductRegistration.findProjectsAccordingSerialNum('11111', '123','EGO');
            CCM_ProductRegistration.Product3 p3 = new CCM_ProductRegistration.Product3(wi);
            List<CCM_ProductRegistration.Product3> p3List = new List<CCM_ProductRegistration.Product3>();
            p3List.add(p3);
            CCM_ProductRegistration.WarrantyItemWrapper wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
            
        	CCM_ProductRegistration.checkSNAndUpdateIndicator('Skil', Json.serialize(wiNew));
            CCM_ProductRegistration.checkSNAndUpdateIndicator('SkilSaw', Json.serialize(wiNew));
            CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', Json.serialize(wiNew));
            wi.Serial_Number__c = 'NLM';
            update wi;
            CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', Json.serialize(wiNew));
        	
        }
        
          
    }

    //Add by Abby on 04/03/2020
    static testMethod void testMethods3(){
        List<RecordType> kitRecordType = [SELECT Id, Name FROM RecordType 
                                                WHERE SObjectType = 'Product2' AND DeveloperName = 'Kit'];
        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType 
                                                    WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];

        Recordtype rtd = [SELECT Id FROM RecordType WHERE Name = 'Products and Parts' and sobjecttype = 'Kit_item__c' LIMIT 1];

        Account acc = TestDataFactory.createAccount();
        acc.Product_Type__c = 'Skil';
        acc.Phone = '********';
        acc.PersonEmail = '<EMAIL>';
        acc.TaxID__c = 'testTax';
        insert acc;

        List<Product2> prodList = new List<Product2>();
        Product2 masterProd = new Product2();
        masterProd.Name = 'Test';
        masterProd.Brand_Name__c = 'Skil';
        masterProd.RecordTypeId = kitRecordType[0].Id;
        masterProd.ProductCode = 'CB738401';
        masterProd.Country_of_Origin__c = 'United States';
        masterProd.IsActive = true;
        masterProd.Source__c = 'EBS';
        prodList.add(masterProd);

        Product2 kitItemProd1 = new Product2();
        kitItemProd1.Name = 'Test';
        kitItemProd1.Brand_Name__c = 'Skil';
        kitItemProd1.RecordTypeId = proRecordType[0].Id;
        kitItemProd1.ProductCode = 'BY519901';
        kitItemProd1.Country_of_Origin__c = 'United States';
        kitItemProd1.IsActive = true;
        kitItemProd1.Source__c = 'EBS';
        prodList.add(kitItemProd1);

        Product2 kitItemProd2 = new Product2();
        kitItemProd2.Name = 'Test';
        kitItemProd2.Brand_Name__c = 'Skil';
        kitItemProd2.RecordTypeId = proRecordType[0].Id;
        kitItemProd2.ProductCode = 'SC536501';
        kitItemProd2.Country_of_Origin__c = 'United States';
        kitItemProd2.IsActive = true;
        kitItemProd2.Source__c = 'EBS';
        prodList.add(kitItemProd2);

        Product2 kitItemProd3 = new Product2();
        kitItemProd3.Name = 'Test';
        kitItemProd3.Brand_Name__c = 'Skil';
        kitItemProd3.RecordTypeId = proRecordType[0].Id;
        kitItemProd3.ProductCode = 'BY519901T';
        kitItemProd3.Country_of_Origin__c = 'United States';
        kitItemProd3.IsActive = true;
        kitItemProd3.Source__c = 'EBS';
        prodList.add(kitItemProd3);
        insert prodList;

        List<Kit_item__c> kitItemList = new List<Kit_Item__c>();
        Kit_item__c kitItem1 = new Kit_Item__c();
        kitItem1.Kit__c = masterProd.Id;
        kitItem1.Product__c = kitItemProd1.Id;
        kitItem1.RecordTypeId = rtd.Id;
        kitItem1.Source__c = 'EBS';
        kitItem1.Sequence__c = 'test';
        kitItem1.Repairable__c = true;
        kitItemList.add(kitItem1);

        Kit_item__c kitItem2 = new Kit_Item__c();
        kitItem2.Kit__c = masterProd.Id;
        kitItem2.Product__c = kitItemProd2.Id;
        kitItem2.RecordTypeId = rtd.Id;
        kitItem2.Source__c = 'EBS';
        kitItem2.Repairable__c = true;
        kitItemList.add(kitItem2);

        Kit_item__c kitItem3 = new Kit_Item__c();
        kitItem3.Kit__c = masterProd.Id;
        kitItem3.Product__c = kitItemProd3.Id;
        kitItem3.RecordTypeId = rtd.Id;
        kitItem3.Source__c = 'EBS';
        kitItem3.Sequence__c = 'test';
        kitItem3.Repairable__c = true;
        kitItemList.add(kitItem3);
        insert kitItemList;

        Warranty__c warrantyInfo = new Warranty__c();
        warrantyInfo.AccountCustomer__c = acc.Id;
        warrantyInfo.Master_Product__c = masterProd.Id;
        warrantyInfo.Brand_Name__c = 'Skil';
        warrantyInfo.Product_Use_Type2__c = 'Professional/Commercial';
        warrantyInfo.Lost_Receipt__c = True;
        insert warrantyInfo;

        List<Warranty_Item__c> warrantyItemList = new List<Warranty_Item__c>(); 
        Warranty_Item__c wItem1 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem1.Product__c = kitItemProd1.Id;
        wItem1.Serial_Number__c = 'NLM01190500001';
        wItem1.Production_Date__c =System.today().adddays(-180);
        warrantyItemList.add(wItem1);

        Warranty_Item__c wItem2 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem2.Product__c = kitItemProd1.Id;
        wItem2.Serial_Number__c = 'NLM01190500002';
        wItem2.Production_Date__c =System.today().adddays(-180);
        warrantyItemList.add(wItem2);

        Warranty_Item__c wItem3 = TestDataFactory.createWarrantyItem(warrantyInfo.Id);
        wItem3.Product__c = kitItemProd1.Id;
        wItem3.Serial_Number__c = 'NLM01190500003';
        wItem3.Production_Date__c =System.today().adddays(-180);
        warrantyItemList.add(wItem3);
        insert warrantyItemList;

        CCM_ProductRegistration.changeMasterProduct(masterProd.Id);
    }

    @isTest 
    static void testSaveSurvey() {
        Test.startTest();
        Account objEndUserAccount = new Account(LastName = 'test', RecordTypeId = CCM_Constants.CUSTOMER_END_USER_ACCOUNT_RECORD_TYPE_ID);
        objEndUserAccount.TaxID__c = 'testTax';
        insert objEndUserAccount;
        Warranty__c objWarranty = new Warranty__c(AccountCustomer__c = objEndUserAccount.Id);
        insert objWarranty;
        Chervon_Survey__c objSurvey = new Chervon_Survey__c(Name = 'test');
        insert objSurvey;
        Chervon_Survey_Question__c objQuestion = new Chervon_Survey_Question__c(Chervon_Survey__c = objSurvey.Id, Type__c = 'Single Select', Order__c = 1, Question__c = 'test1');
        insert objQuestion;
        Chervon_Survey_Question_Choice__c objChoice = new Chervon_Survey_Question_Choice__c(
            Chervon_Survey_Question__c = objQuestion.Id,
            Order__c = 1,
            Choice__c = 'test',
            RequireManualInput__c = true
        );
        insert objChoice;
        Product2 objProduct = new Product2(Name = 'test', Chervon_Survey__c = objSurvey.Id);
        insert objProduct;
        insert new Warranty_Item__c(Warranty__c = objWarranty.Id, Product__c = objProduct.Id, Serial_Number__c = 'test');
        // prettier-ignore
        String strResponse =
            '[' +
            '  {' +
            '    "questionId":"' + objQuestion.Id + '",' +
            '    "result": {' +
            '      "type": "Single Select",' +
            '      "answer": "' + objChoice.Id + '",' +
            '      "haveManualText": true,' +
            '      "manualText": "test1",' +
            '      "manualSpecialChoice": "' + objChoice.Id + '"' +
            '    }' +
            '  }' +
            ']';
        try {
            CCM_ProductRegistration.saveSurvey(null, objEndUserAccount.Id, objWarranty.Id, strResponse);
        } catch (Exception objE) {
            System.assert(objE.getMessage().contains('Bad Request'));
        }
        try {
            CCM_ProductRegistration.saveSurvey(objSurvey.Id, objEndUserAccount.Id, objWarranty.Id, strResponse);
        } catch (Exception objE) {
            System.assertEquals(objE.getMessage(), 'test');
        }
        Test.stopTest();
        System.assertEquals([SELECT COUNT() FROM Log__c], 3);
    }

    @isTest 
    static void testCheckSN1() {
        List<RecordType> acRecordType = [SELECT Id, Name FROM RecordType WHERE SObjectType = 'Account' AND DeveloperName = 'PersonAccount'] ;
        Account ac = new Account();
        ac.LastName = 'testCheckSN';
        ac.FirstName = 'testCheckSN';
        ac.ShippingPostalCode = '60007';
        ac.RecordTypeId = acRecordType[0].Id;
        ac.PersonEmail = '<EMAIL>';
        ac.Distributor_or_Dealer__c = 'Dealer';
        ac.TaxID__c = 'testTax';
        insert ac;

        List<RecordType> proRecordType = [SELECT Id, Name FROM RecordType WHERE SObjectType = 'Product2' AND DeveloperName = 'Product'];
        List<RecordType> partRecordType = [SELECT Id, Name FROM RecordType WHERE SObjectType = 'Product2' AND DeveloperName = 'Parts'];

        Product2 pro = new Product2();
        pro.Name = 'testCheckSN';
        pro.Brand_Name__c = 'EGO';
        pro.RecordTypeId = proRecordType[0].Id;
        pro.ProductCode = '1234567';
        pro.IsActive = true;
        pro.Source__c = 'PIM';
        insert pro;

        Product2 par = new Product2();
        par.Name = 'testCheckSNpar';
        par.Brand_Name__c = 'EGO';
        par.RecordTypeId = partRecordType[0].Id;
        par.ProductCode = pro.ProductCode;
        par.IsActive = true;
        par.Source__c = 'PIM';
        insert par;

        Warranty__c wrty = TestDataFactory.createWarranty();
        wrty.Master_Product__c = pro.Id;
        wrty.AccountCustomer__c = ac.Id;
        wrty.Purchase_Date__c = Date.today();
        insert wrty;

        Warranty_Item__c wi = TestDataFactory.createWarrantyItem(wrty.Id);
        wi.Product__c = par.Id;
        wi.Serial_Number__c = '123/*********';

        CCM_ProductRegistration.Product3 p3 = new CCM_ProductRegistration.Product3(wi);
        List<CCM_ProductRegistration.Product3> p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        CCM_ProductRegistration.WarrantyItemWrapper wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('FLEX', Json.serialize(wiNew));

        wi.Serial_Number__c = '12345t6789987';
        p3 = new CCM_ProductRegistration.Product3(wi);
        p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('FLEX', Json.serialize(wiNew));

        wi.Serial_Number__c = '1234567899877';
        p3 = new CCM_ProductRegistration.Product3(wi);
        p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('FLEX', Json.serialize(wiNew));

        wi.Serial_Number__c = '1234123899877';
        p3 = new CCM_ProductRegistration.Product3(wi);
        p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('FLEX', Json.serialize(wiNew));
        
        wi.Serial_Number__c = '1234123899877123';
        wi.Product_Code__c = 'FC12';
        p3 = new CCM_ProductRegistration.Product3(wi);
        p3List = new List<CCM_ProductRegistration.Product3>();
        p3List.add(p3);
        wiNew = new CCM_ProductRegistration.WarrantyItemWrapper('1234567', p3List);
        CCM_ProductRegistration.checkSNAndUpdateIndicator('EGO', Json.serialize(wiNew));
        CCM_ProductRegistration.testforcoveragennn1();    
    }
}