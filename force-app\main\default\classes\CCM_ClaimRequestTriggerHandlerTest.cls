/**
 * @Author:     AI Assistant
 * @Date:       2024-12-19
 * @Return:     
 * @Function:   CCM_ClaimRequestTriggerHandler的测试类
 * @Last_Modified_by:  
 * @Last_Modified_time:
 * @Modifiy_Purpose:   提升测试覆盖率到90%
 */
@IsTest
public class CCM_ClaimRequestTriggerHandlerTest {
    
    @TestSetup
    static void setupTestData() {
        // 创建Account
        Account testAccount = new Account();
        testAccount.Name = '测试客户';
        testAccount.TaxID__c = 'TEST001';
        testAccount.AccountNumber = 'ACC001';
        insert testAccount;
        
        // 创建Claim_Request__c
        Claim_Request__c claimRequest = new Claim_Request__c();
        claimRequest.Customer__c = testAccount.Id;
        claimRequest.Claim_Status__c = 'Draft';
        // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
        insert claimRequest;
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestTriggerHandler的拒绝验证
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testRejectValidation() {
        Claim_Request__c claimRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c LIMIT 1];
        claimRequest.Claim_Status__c = 'Pending Review';
        update claimRequest;
        
        Test.startTest();
        // 测试拒绝时没有选择拒绝原因的错误
        claimRequest.Claim_Status__c = 'Rejected';
        claimRequest.Reject_Reason__c = null;
        
        try {
            update claimRequest;
            System.assert(false, '应该抛出验证错误');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Please select reject reason before reject this request!'), '错误消息不正确');
        }
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestTriggerHandler的正常拒绝流程
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testValidReject() {
        Claim_Request__c claimRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c LIMIT 1];
        claimRequest.Claim_Status__c = 'Pending Review';
        update claimRequest;
        
        Test.startTest();
        // 测试正常拒绝流程
        claimRequest.Claim_Status__c = 'Rejected';
        claimRequest.Reject_Reason__c = 'Invalid documentation';
        
        update claimRequest;
        
        // 验证更新成功
        Claim_Request__c updatedRequest = [SELECT Id, Claim_Status__c, Reject_Reason__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals('Rejected', updatedRequest.Claim_Status__c, '状态应该已更新为Rejected');
        System.assertEquals('Invalid documentation', updatedRequest.Reject_Reason__c, '拒绝原因应该已设置');
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestTriggerHandler的其他状态变更
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testOtherStatusChanges() {
        Claim_Request__c claimRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c LIMIT 1];
        
        Test.startTest();
        // 测试从Draft到Pending Review的状态变更
        claimRequest.Claim_Status__c = 'Pending Review';
        update claimRequest;
        
        // 验证更新成功
        Claim_Request__c updatedRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals('Pending Review', updatedRequest.Claim_Status__c, '状态应该已更新为Pending Review');
        
        // 测试从Pending Review到Approved的状态变更
        claimRequest.Claim_Status__c = 'Approved';
        update claimRequest;
        
        updatedRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals('Approved', updatedRequest.Claim_Status__c, '状态应该已更新为Approved');
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestTriggerHandler的批量处理
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testBulkProcessing() {
        Account testAccount = [SELECT Id FROM Account LIMIT 1];
        
        // 创建多个Claim Request
        List<Claim_Request__c> claimRequests = new List<Claim_Request__c>();
        for (Integer i = 0; i < 5; i++) {
            Claim_Request__c claimRequest = new Claim_Request__c();
            claimRequest.Customer__c = testAccount.Id;
            claimRequest.Claim_Status__c = 'Draft';
            // claimRequest.Claim_Type__c = 'Sell Through'; // 移除不存在的字段
            claimRequests.add(claimRequest);
        }
        insert claimRequests;
        
        Test.startTest();
        // 批量更新状态
        for (Claim_Request__c cr : claimRequests) {
            cr.Claim_Status__c = 'Pending Review';
        }
        update claimRequests;
        
        // 验证批量更新成功
        List<Claim_Request__c> updatedRequests = [SELECT Id, Claim_Status__c FROM Claim_Request__c WHERE Id IN :claimRequests];
        for (Claim_Request__c cr : updatedRequests) {
            System.assertEquals('Pending Review', cr.Claim_Status__c, '所有记录状态应该已更新');
        }
        Test.stopTest();
    }
    
    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:     
     * @Function:   测试CCM_ClaimRequestTriggerHandler的边界条件
     * @Last_Modified_by:  
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testEdgeCases() {
        Claim_Request__c claimRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c LIMIT 1];
        
        Test.startTest();
        // 测试从非Pending Review状态直接到Rejected（不应该触发验证）
        claimRequest.Claim_Status__c = 'Rejected';
        claimRequest.Reject_Reason__c = null;
        
        update claimRequest;
        
        // 验证更新成功（因为不是从Pending Review状态变更）
        Claim_Request__c updatedRequest = [SELECT Id, Claim_Status__c FROM Claim_Request__c WHERE Id = :claimRequest.Id];
        System.assertEquals('Rejected', updatedRequest.Claim_Status__c, '状态应该已更新为Rejected');
        Test.stopTest();
    }
}
