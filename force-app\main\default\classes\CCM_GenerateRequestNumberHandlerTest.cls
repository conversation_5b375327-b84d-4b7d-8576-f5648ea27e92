/**
 * CCM_GenerateRequestNumberHandler of test class
 */
@IsTest
public class CCM_GenerateRequestNumberHandlerTest {
    @TestSetup
    static void testSetUpData() {
        CCM_SharingUtil.isSharingOnly = true;
        Account account = new Account();
        account.Name = 'testAccount';
        account.TaxID__c = 'testtax';
        insert account;

        Purchase_Order__c po1 = new Purchase_Order__c();
        po1.Customer__c = account.Id;
        po1.Is_Delegate__c = false;
        po1.Status__c = 'Draft';
        Po1.Sync_Status__c = '';
        po1.Expected_Delivery_Date__c = Date.today();
        po1 = (Purchase_Order__c)CCM_TESTDataUtil.createSobject(po1, 'Place_Order');

        Reverse_Order_Request__c reverseOrderRequest = new Reverse_Order_Request__c();
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Warehouse';
        reverseOrderRequest.Customer__c = account.Id;
        reverseOrderRequest.Purchase_Order__c = po1.Id;
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED;
        insert reverseOrderRequest;

        Reverse_Order_Item__c reverseOrderItem = new Reverse_Order_Item__c();
        reverseOrderItem.Reverse_Order_Request__c = reverseOrderRequest.Id;
        reverseOrderItem.Order_Product_Type__c = 'Overage';
        insert reverseOrderItem;

    }

    @IsTest
    static void testBeforeTrigger() {
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'OPS Approved';
        update reverseOrderRequest;
    }
    @IsTest
    static void testCCM_ReverseOrderRequestHandler(){
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Transportation';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Others';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS not confirmed';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_MORE_THAN_I_ORDERED;
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Warehouse';
        update reverseOrderRequest;
        Test.stopTest();
    }

    @IsTest
    static void testCCM_ReverseOrderRequestHandler6(){
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'Rejected';
        update reverseOrderRequest;

        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_MORE_THAN_I_ORDERED;
        reverseOrderRequest.Approval_Status__c = 'Approved';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Warehouse';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Transportation';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Others';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS not confirmed';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;
        Test.stopTest();
    }

    @IsTest
    static void testCCM_ReverseOrderRequestHandler2(){
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'Approved';
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WRONG_PRODUCT;
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Warehouse';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Transportation';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Others';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS not confirmed';
        update reverseOrderRequest;

    }

    @IsTest
    static void testCCM_ReverseOrderRequestHandler3(){
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Transportation';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Others';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS not confirmed';
        update reverseOrderRequest;

    }

    @IsTest
    static void testCCM_ReverseOrderRequestHandler4(){
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_PRODUCT_DAMAGED_IN_SHIPMENT;
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Transportation';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS not confirmed';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_WITH_NO_REASON;
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Rejected';
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_ERROR_BY_SALES;
        update reverseOrderRequest;

        reverseOrderRequest.Approval_Status__c = 'Approved';
        update reverseOrderRequest;
        Test.stopTest();
    }
}