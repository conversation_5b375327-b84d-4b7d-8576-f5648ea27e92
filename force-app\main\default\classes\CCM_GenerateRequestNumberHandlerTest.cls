/**
 * CCM_GenerateRequestNumberHandler of test class
 */
@IsTest
public class CCM_GenerateRequestNumberHandlerTest {
    @TestSetup
    static void testSetUpData() {
        CCM_SharingUtil.isSharingOnly = true;

        // 创建Account
        Account account = new Account();
        account.Name = 'testAccount';
        account.TaxID__c = 'testtax';
        account.AccountNumber = 'TEST001';
        insert account;

        // 创建Account_Address__c
        Account_Address__c accountAddress = new Account_Address__c();
        accountAddress.Customer__c = account.Id;
        accountAddress.Address1__c = 'Test Street 123';
        accountAddress.City__c = 'Test City';
        accountAddress.State__c = 'CA';
        accountAddress.Postal_Code__c = '12345';
        insert accountAddress;

        // 创建Address_With_Program__c
        Address_With_Program__c addressWithProgram = new Address_With_Program__c();
        addressWithProgram.Account_Address__c = accountAddress.Id;
        insert addressWithProgram;

        // 创建Order
        Order ord = new Order();
        ord.AccountId = account.Id;
        ord.EffectiveDate = Date.today();
        ord.Status = 'Draft';
        ord.Order_Number__c = 'ORD001';
        insert ord;

        // 创建Purchase_Order__c并设置必填字段
        Purchase_Order__c po1 = new Purchase_Order__c();
        po1.Customer__c = account.Id;
        po1.Is_Delegate__c = false;
        po1.Status__c = 'Draft';
        po1.Sync_Status__c = '';
        po1.Expected_Delivery_Date__c = Date.today();
        po1.BillTo__c = addressWithProgram.Id;  // 设置必填字段
        po1.ShipTo__c = addressWithProgram.Id;  // 设置必填字段
        insert po1;

        Reverse_Order_Request__c reverseOrderRequest = new Reverse_Order_Request__c();
        reverseOrderRequest.Confirmed_by_OPS__c = 'OPS confirmed – Warehouse';
        reverseOrderRequest.Customer__c = account.Id;
        reverseOrderRequest.Purchase_Order__c = po1.Id;
        reverseOrderRequest.Order__c = ord.Id;
        reverseOrderRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED;
        reverseOrderRequest.BillTo__c = addressWithProgram.Id;  // 设置必填字段
        reverseOrderRequest.ShipTo__c = addressWithProgram.Id;  // 设置必填字段
        insert reverseOrderRequest;

        Reverse_Order_Item__c reverseOrderItem = new Reverse_Order_Item__c();
        reverseOrderItem.Reverse_Order_Request__c = reverseOrderRequest.Id;
        reverseOrderItem.Order_Product_Type__c = 'Overage';
        insert reverseOrderItem;
    }

    @IsTest
    static void testBeforeTrigger() {
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id,Approval_Status__c FROM Reverse_Order_Request__c LIMIT 1];
        reverseOrderRequest.Approval_Status__c = 'OPS Approved';
        update reverseOrderRequest;
    }


    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试CCM_GenerateRequestNumberHandler的错误验证场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testValidationErrors() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id, Approval_Status__c, Confirmed_by_OPS__c FROM Reverse_Order_Request__c LIMIT 1];

        // 测试OPS Approved但没有确认的错误
        reverseOrderRequest.Confirmed_by_OPS__c = null;
        reverseOrderRequest.Approval_Status__c = 'OPS Approved';

        try {
            update reverseOrderRequest;
            System.assert(false, '应该抛出错误');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Please update ops verification result first!'), '错误消息不正确');
        }
        Test.stopTest();
    }

    /**
     * @Author:     AI Assistant
     * @Date:       2024-12-19
     * @Return:
     * @Function:   测试CCM_GenerateRequestNumberHandler的Inside Sales验证场景
     * @Last_Modified_by:
     * @Last_Modified_time:
     * @Modifiy_Purpose:   提升测试覆盖率到90%
     */
    @IsTest
    static void testInsideSalesValidation() {
        Test.startTest();
        Reverse_Order_Request__c reverseOrderRequest = [SELECT Id, Current_Approver__c, Shipment__c FROM Reverse_Order_Request__c LIMIT 1];

        // 测试Inside Sales需要delivery number的验证
        reverseOrderRequest.Current_Approver__c = 'Inside Sales';
        reverseOrderRequest.Shipment__c = null;

        try {
            update reverseOrderRequest;
            System.assert(false, '应该抛出错误');
        } catch (DmlException e) {
            System.assert(e.getMessage().contains('Please add delivery number first!'), '错误消息不正确');
        }
        Test.stopTest();
    }

    // /**
    //  * @Author:     AI Assistant
    //  * @Date:       2024-12-19
    //  * @Return:
    //  * @Function:   测试CCM_GenerateRequestNumberHandler的请求编号生成场景
    //  * @Last_Modified_by:
    //  * @Last_Modified_time:
    //  * @Modifiy_Purpose:   提升测试覆盖率到90%
    //  */
    // @IsTest
    // static void testRequestNumberGeneration() {
    //     Test.startTest();

    //     // 创建新的Reverse Order Request来测试编号生成
    //     Account account = [SELECT Id FROM Account LIMIT 1];
    //     Order ord = [SELECT Id FROM Order LIMIT 1];
    //     Purchase_Order__c po = [SELECT Id FROM Purchase_Order__c LIMIT 1];

    //     Reverse_Order_Request__c newRequest = new Reverse_Order_Request__c();
    //     newRequest.Customer__c = account.Id;
    //     newRequest.Order__c = ord.Id;
    //     newRequest.Purchase_Order__c = po.Id;
    //     newRequest.External_Return_Reason__c = CCM_ReverseOrderUtil.EXTERNAL_RETURN_REASON_LESS_THAN_I_ORDERED;

    //     insert newRequest;

    //     // 验证请求编号已生成
    //     Reverse_Order_Request__c insertedRequest = [SELECT Id, Reverse_Order_Request_Number__c, Name, Counter__c FROM Reverse_Order_Request__c WHERE Id = :newRequest.Id];
    //     System.assertNotEquals(null, insertedRequest.Reverse_Order_Request_Number__c, '请求编号应该已生成');
    //     System.assertEquals(insertedRequest.Reverse_Order_Request_Number__c, insertedRequest.Name, '名称应该等于请求编号');
    //     System.assertNotEquals(null, insertedRequest.Counter__c, '计数器应该已设置');

    //     Test.stopTest();
    // }
}